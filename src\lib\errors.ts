// Error types and handling utilities

export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UPLOAD_ERROR = 'UPLOAD_ERROR',
  TRANSFORMATION_ERROR = 'TRANSFORMATION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface AppError {
  type: ErrorType;
  message: string;
  details?: string;
  retryable?: boolean;
  resetTime?: number;
}

export class CustomError extends Error {
  public type: ErrorType;
  public retryable: boolean;
  public resetTime?: number;

  constructor(type: ErrorType, message: string, retryable: boolean = false, resetTime?: number) {
    super(message);
    this.type = type;
    this.retryable = retryable;
    this.resetTime = resetTime;
    this.name = 'CustomError';
  }
}

export function parseApiError(response: Response, data?: any): AppError {
  if (response.status === 429) {
    const resetTime = response.headers.get('X-RateLimit-Reset');
    return {
      type: ErrorType.RATE_LIMIT_ERROR,
      message: 'Too many requests. Please wait before trying again.',
      details: data?.error || 'Rate limit exceeded',
      retryable: true,
      resetTime: resetTime ? parseInt(resetTime) : undefined
    };
  }

  if (response.status === 400) {
    return {
      type: ErrorType.VALIDATION_ERROR,
      message: 'Invalid request. Please check your input.',
      details: data?.error || 'Bad request',
      retryable: false
    };
  }

  if (response.status >= 500) {
    return {
      type: ErrorType.NETWORK_ERROR,
      message: 'Server error. Please try again later.',
      details: data?.error || 'Internal server error',
      retryable: true
    };
  }

  return {
    type: ErrorType.UNKNOWN_ERROR,
    message: 'An unexpected error occurred.',
    details: data?.error || response.statusText,
    retryable: true
  };
}

export function getErrorMessage(error: unknown): AppError {
  if (error instanceof CustomError) {
    return {
      type: error.type,
      message: error.message,
      retryable: error.retryable,
      resetTime: error.resetTime
    };
  }

  if (error instanceof Error) {
    // Check for specific error patterns
    if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
      return {
        type: ErrorType.NETWORK_ERROR,
        message: 'Network connection failed. Please check your internet connection.',
        details: error.message,
        retryable: true
      };
    }

    if (error.message.includes('Upload failed')) {
      return {
        type: ErrorType.UPLOAD_ERROR,
        message: 'Failed to upload image. Please try again.',
        details: error.message,
        retryable: true
      };
    }

    if (error.message.includes('Failed to save image record')) {
      return {
        type: ErrorType.DATABASE_ERROR,
        message: 'Failed to save your image. The transformation was successful, but we couldn\'t save it to your gallery.',
        details: error.message,
        retryable: true
      };
    }

    if (error.message.includes('Failed to fetch images')) {
      return {
        type: ErrorType.DATABASE_ERROR,
        message: 'Failed to load gallery images. Please refresh the page.',
        details: error.message,
        retryable: true
      };
    }
  }

  return {
    type: ErrorType.UNKNOWN_ERROR,
    message: 'An unexpected error occurred. Please try again.',
    details: error?.toString() || 'Unknown error',
    retryable: true
  };
}

export function formatTimeRemaining(resetTime: number): string {
  const now = Date.now();
  const remaining = Math.max(0, resetTime - now);
  const seconds = Math.ceil(remaining / 1000);
  
  if (seconds < 60) {
    return `${seconds} second${seconds !== 1 ? 's' : ''}`;
  }
  
  const minutes = Math.ceil(seconds / 60);
  return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
}

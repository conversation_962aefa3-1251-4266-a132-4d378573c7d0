# Source Architecture Analysis

## 1. Component Hierarchy and Relationships

The application follows a standard Next.js project structure:

- **`src/app/layout.tsx`**: The root layout of the application, which includes the navigation bar and wraps all other pages.
- **`src/app/page.tsx`**: The main page of the application. It renders the `HeroSection` and `UploadZone` components.
- **`src/app/gallery/page.tsx`**: The gallery page, which displays a masonry layout of transformed images fetched from Supabase.
- **`src/components/`**: This directory contains reusable React components.
  - **`UploadZone.tsx`**: This is a major component that handles image uploading, style selection, and the transformation process. It orchestrates the client-side logic for the core feature. It's composed of smaller components like `StyleCard` and `ErrorDisplay`.
  - **`HeroSection.tsx`**: A presentational component for the hero section of the main page.
  - **`StyleCard.tsx`**: A card component to display and select an image transformation style.
  - **`ErrorDisplay.tsx`**: A component for displaying various types of errors to the user.
  - **`ImageUploadSection.tsx`**, **`StyleSelectionSection.tsx`**, **`TransformationResult.tsx`**: These components were likely refactored into the larger `UploadZone.tsx` but show the logical separation of concerns.

## 2. Data Flow Patterns

- **Client-Side Data Flow**: The user interacts with the `UploadZone` component to select an image and a style. This state is managed within the `UploadZone` component using React's `useState`.
- **API Interaction**: When the "Transform" button is clicked, the `UploadZone` component sends a `POST` request with the image and style to the `/api/generate` endpoint.
- **Server-Side Data Flow**:
  1. The `/api/generate` route handler receives the request.
  2. It performs rate-limiting and input validation.
  3. It calls the Google Gemini API to perform the image transformation.
  4. The original image is uploaded to Supabase Storage, and a record of the transformation is saved in the Supabase database.
  5. The transformed image URL is returned to the client.
- **Gallery Data Flow**: The `gallery/page.tsx` component fetches the list of transformed images from the Supabase database via the `getImages` function in `src/lib/supabase.ts`.

## 3. Critical Business Logic Modules

- **`src/app/api/generate/route.ts`**: This is the core of the backend business logic. It handles the image transformation by interacting with the Google Gemini API. It also includes rate-limiting and validation.
- **`src/lib/validation.ts`**: This module is critical for security and data integrity. It provides functions to validate image files, styles, and form data. It defines the allowed image types, file sizes, and transformation styles.
- **`src/lib/supabase.ts`**: This module encapsulates all interactions with the Supabase backend, including image storage and database operations. It's crucial for persisting data.
- **`src/lib/rateLimit.ts`**: Implements a simple in-memory rate-limiting mechanism to prevent abuse of the API.
- **`src/lib/errors.ts`**: This module is central to the error handling strategy, defining custom error types and providing utility functions to parse and display errors.

## 4. Error Handling Strategies

- **Custom Error Types**: The application defines a set of custom error types in `src/lib/errors.ts` (e.g., `NETWORK_ERROR`, `RATE_LIMIT_ERROR`, `VALIDATION_ERROR`). This allows for consistent and specific error handling.
- **API Error Parsing**: The `parseApiError` function in `src/lib/errors.ts` is used to interpret errors from the API based on the HTTP status code and response body.
- **User-Facing Error Display**: The `ErrorDisplay.tsx` component provides a user-friendly way to show errors. It can display different icons and messages based on the error type and even provide a "Retry" button for retryable errors.
- **Graceful Degradation**: In the `UploadZone` component, if saving the image record to the database fails after a successful transformation, the error is logged to the console, but the user still sees the transformed image.

## 5. State Management Approach

- **Local Component State**: The application primarily uses local component state (`useState`) within the `UploadZone` and `GalleryPage` components to manage UI state, such as the selected file, selected style, loading status, and errors.
- **No Global State Management**: There is no evidence of a global state management library like Redux or Zustand being actively used. For the current scope of the application, local state management is sufficient. The state is passed down through props where necessary.

import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from "@google/generative-ai";
import { NextResponse } from "next/server";
import { rateLimiter, getClientIdentifier } from "../../../lib/rateLimit";
import { validateFormData } from "../../../lib/validation";

export const runtime = 'edge';

export async function POST(request: Request) {
  try {
    // Apply rate limiting
    const clientId = getClientIdentifier(request);
    const rateLimitResult = rateLimiter.isAllowed(clientId);

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded. Please try again later.',
          resetTime: rateLimitResult.resetTime
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': '5',
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateLimitResult.resetTime?.toString() || '',
          }
        }
      );
    }
    const formData = await request.formData();

    // Validate input data
    const validation = validateFormData(formData);
    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: 'Invalid input data',
          details: validation.errors.join(', ')
        },
        { status: 400 }
      );
    }

    const image = formData.get('image') as File;
    const style = formData.get('style') as string;

    // Convert image to base64
    const buffer = await image.arrayBuffer();
    const base64Image = Buffer.from(buffer).toString('base64');

    // Initialize Gemini
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

    // Generate image
    const result = await model.generateContent({
      contents: [
        { role: "user", parts: [{ text: `Apply ${style} style to this image` }, { inlineData: { data: base64Image, mimeType: image.type } }] }
      ],
      generationConfig: {
        responseMimeType: "application/json"
      },
      safetySettings: [{
        category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH
      }]
    });

    // Assuming Gemini returns a JSON with a 'transformedImage' field
    const responseData = JSON.parse(result.response.text());
    const transformedImageUrl = responseData.transformedImage || 'https://via.placeholder.com/500x500.png?text=Transformed+Image';

    return NextResponse.json(
      { image: transformedImageUrl },
      {
        headers: {
          'X-RateLimit-Limit': '5',
          'X-RateLimit-Remaining': rateLimitResult.remaining?.toString() || '0',
        }
      }
    );
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
'use client';

import React, { useEffect, useState } from 'react';
import Masonry from 'react-masonry-css';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { getImages, ImageRecord } from '../../lib/supabase';
import ErrorDisplay from '../../components/ErrorDisplay';
import { getErrorMessage, AppError } from '../../lib/errors';

const breakpointColumnsObj = {
  default: 4,
  1100: 3,
  700: 2,
  500: 1
};

const GalleryPage: React.FC = () => {
  const [images, setImages] = useState<ImageRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<AppError | null>(null);

  useEffect(() => {
    fetchImages();
  }, []);

  const fetchImages = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getImages();
      setImages(data);
    } catch (err: unknown) {
      const errorInfo = getErrorMessage(err);
      setError(errorInfo);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center text-arcane-light">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-arcane-primary mx-auto mb-4"></div>
          <p className="text-xl">Loading gallery...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-8">
        <div className="max-w-md w-full">
          <ErrorDisplay error={error} onRetry={fetchImages} />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-8 bg-gray-950 text-arcane-light min-h-screen">
      <h1 className="text-5xl font-arcane font-bold text-center mb-12">Art Gallery</h1>
      {images.length === 0 ? (
        <div className="text-center text-xl text-gray-400">No transformed images yet. Start transforming on the home page!</div>
      ) : (
        <Masonry
          breakpointCols={breakpointColumnsObj}
          className="my-masonry-grid"
          columnClassName="my-masonry-grid_column"
        >
          {images.map((image) => (
            <motion.div
              key={image.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              className="relative rounded-lg overflow-hidden shadow-lg group"
            >
              <Image
                src={image.transformed_url}
                alt={`Transformed image with ${image.style} style`}
                width={500}
                height={500}
                style={{ width: '100%', height: 'auto' }}
                className="rounded-lg"
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <p className="text-white text-lg font-semibold">{image.style}</p>
              </div>
            </motion.div>
          ))}
        </Masonry>
      )}
    </div>
  );
};

export default GalleryPage;
import type { <PERSON>ada<PERSON> } from "next";
import { Cinzel_Decorative, Manrope } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/components/auth/AuthProvider";
import { Navigation } from "@/components/layout/Navigation";
import { DevelopmentNotice } from "@/components/ui/DevelopmentNotice";

const cinzel = Cinzel_Decorative({
  weight: ["400", "700", "900"],
  subsets: ["latin"],
  variable: "--font-cinzel-decorative",
});

const manrope = Manrope({
  variable: "--font-manrope",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "Arcane Artify - Transform Images with Dark Magic",
  description: "Transform your images with mystical AI-powered artistic styles. Create stunning dark-magical artwork with our advanced image generation technology.",
  keywords: ["AI art", "image transformation", "dark magic", "artistic styles", "image generation"],
  authors: [{ name: "Arcane Artify" }],
  openGraph: {
    title: "Arcane Artify - Transform Images with Dark Magic",
    description: "Transform your images with mystical AI-powered artistic styles.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Arcane Artify - Transform Images with Dark Magic",
    description: "Transform your images with mystical AI-powered artistic styles.",
  },
};

export function generateViewport() {
  return {
    width: 'device-width',
    initialScale: 1,
    themeColor: '#7e3ff2',
  };
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${cinzel.variable} ${manrope.variable} dark`}>
      <body className="antialiased">
        <AuthProvider>
          <div className="min-h-screen bg-base">
            <DevelopmentNotice />
            <Navigation />
            <main>{children}</main>
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}

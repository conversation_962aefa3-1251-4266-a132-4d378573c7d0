import { createBrowserClient } from '@supabase/ssr';
import { AuthError, User, Session } from '@supabase/supabase-js';
import { createContext, useContext, useEffect, useState } from 'react';

// Check if we're in development mode with placeholder values
const isDevelopmentMode =
  !process.env.NEXT_PUBLIC_SUPABASE_URL ||
  process.env.NEXT_PUBLIC_SUPABASE_URL.includes('placeholder') ||
  !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ||
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.includes('placeholder');

// Create Supabase client only if we have real credentials
export const supabase = isDevelopmentMode
  ? null
  : createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

export interface AuthState {
  user: User | null;
  session: Session | null;
  loading: boolean;
  error: AuthError | null;
}

export interface AuthContextValue extends AuthState {
  signIn: (email: string) => Promise<void>;
  signOut: () => Promise<void>;
  clearError: () => void;
}

export const AuthContext = createContext<AuthContextValue | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const useAuthState = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
    error: null
  });

  useEffect(() => {
    // If in development mode, just set loading to false
    if (isDevelopmentMode || !supabase) {
      setAuthState({
        user: null,
        session: null,
        loading: false,
        error: null
      });
      return;
    }

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        setAuthState({
          user: session?.user ?? null,
          session,
          loading: false,
          error
        });
      } catch (error) {
        setAuthState({
          user: null,
          session: null,
          loading: false,
          error: error as AuthError
        });
      }
    };

    getInitialSession();

    // Listen for auth changes (only if supabase is available)
    if (supabase) {
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        async (event, session) => {
          setAuthState({
            user: session?.user ?? null,
            session,
            loading: false,
            error: null
          });

          // Handle sign in success
          if (event === 'SIGNED_IN' && session?.user) {
            // Create or update user profile
            await createUserProfile(session.user);
          }
        }
      );

      return () => subscription.unsubscribe();
    }
  }, []);

  const signIn = async (email: string) => {
    if (isDevelopmentMode || !supabase) {
      // In development mode, just show a message
      alert('Development mode: Supabase not configured. Please add real environment variables to enable authentication.');
      return;
    }

    setAuthState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) throw error;

      setAuthState(prev => ({ ...prev, loading: false }));
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: error as AuthError
      }));
      throw error;
    }
  };

  const signOut = async () => {
    if (isDevelopmentMode || !supabase) {
      // In development mode, just show a message
      alert('Development mode: No user to sign out.');
      return;
    }

    setAuthState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: error as AuthError
      }));
      throw error;
    }
  };

  const clearError = () => {
    setAuthState(prev => ({ ...prev, error: null }));
  };

  return {
    ...authState,
    signIn,
    signOut,
    clearError
  };
};

// Helper function to create user profile
async function createUserProfile(user: User) {
  if (isDevelopmentMode || !supabase) {
    console.log('Development mode: Would create user profile for:', user.email);
    return;
  }

  try {
    const { error } = await supabase
      .from('user_profiles')
      .upsert({
        id: user.id,
        username: user.email?.split('@')[0] || 'user',
        avatar_url: user.user_metadata?.avatar_url,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'id'
      });

    if (error) {
      console.error('Error creating user profile:', error);
    }
  } catch (error) {
    console.error('Error creating user profile:', error);
  }
}

// Types for user profile
export interface UserProfile {
  id: string;
  username: string;
  avatar_url?: string;
  subscription_tier: 'free' | 'pro' | 'premium';
  credits_remaining: number;
  created_at: string;
  updated_at: string;
}

// Get user profile
export async function getUserProfile(userId: string): Promise<UserProfile | null> {
  if (isDevelopmentMode || !supabase) {
    // Return mock profile in development mode
    return {
      id: userId,
      username: 'dev_user',
      avatar_url: undefined,
      subscription_tier: 'free',
      credits_remaining: 10,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }
}

// Update user profile
export async function updateUserProfile(
  userId: string,
  updates: Partial<UserProfile>
): Promise<UserProfile | null> {
  if (isDevelopmentMode || !supabase) {
    console.log('Development mode: Would update user profile:', { userId, updates });
    return null;
  }

  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating user profile:', error);
    return null;
  }
}

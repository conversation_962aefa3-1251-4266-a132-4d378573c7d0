'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ExclamationTriangleIcon, 
  XMarkIcon,
  CodeBracketIcon 
} from '@heroicons/react/24/outline';

export const DevelopmentNotice: React.FC = () => {
  const [isVisible, setIsVisible] = useState(true);

  // Check if we're in development mode
  const isDevelopmentMode = 
    !process.env.NEXT_PUBLIC_SUPABASE_URL || 
    process.env.NEXT_PUBLIC_SUPABASE_URL.includes('placeholder');

  if (!isDevelopmentMode || !isVisible) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        className="fixed top-0 left-0 right-0 z-50 bg-ember/90 backdrop-blur-sm border-b border-ember/20"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <CodeBracketIcon className="w-5 h-5 text-white" />
              <div className="text-white">
                <p className="text-sm font-medium">
                  Development Mode
                </p>
                <p className="text-xs opacity-90">
                  Supabase not configured. Add real environment variables to enable full functionality.
                </p>
              </div>
            </div>
            
            <button
              onClick={() => setIsVisible(false)}
              className="p-1 text-white/80 hover:text-white transition-colors"
              aria-label="Dismiss notice"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

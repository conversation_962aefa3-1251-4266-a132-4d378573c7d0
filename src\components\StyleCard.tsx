"use client";
import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircleIcon } from '@heroicons/react/24/solid';

interface StyleCardProps {
  styleName: string;
  isSelected: boolean;
  onSelect: (style: string) => void;
}

const StyleCard: React.FC<StyleCardProps> = ({ styleName, isSelected, onSelect }) => {
  return (
    <motion.button
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
      onClick={() => onSelect(styleName)}
      className={`
        relative w-full p-4 rounded-xl text-left transition-all duration-300
        focus:outline-none focus:ring-2 focus:ring-cyan focus:ring-offset-2 focus:ring-offset-base
        ${isSelected
          ? 'bg-accent/20 border-2 border-accent text-textPrimary shadow-glow'
          : 'glass-card hover:bg-glass/80 border-2 border-transparent text-textSecondary hover:text-textPrimary'
        }
      `}
      aria-pressed={isSelected}
      role="option"
      aria-selected={isSelected}
    >
      {/* Selection indicator */}
      {isSelected && (
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="absolute top-2 right-2"
        >
          <CheckCircleIcon className="w-5 h-5 text-accent" />
        </motion.div>
      )}

      {/* Style name */}
      <div className="pr-8">
        <h3 className="font-arcane font-semibold text-sm mb-1">
          {styleName}
        </h3>
        <p className="text-xs text-textMuted">
          Mystical transformation
        </p>
      </div>

      {/* Hover effect overlay */}
      <div className={`
        absolute inset-0 rounded-xl transition-opacity duration-300
        ${isSelected ? 'opacity-0' : 'opacity-0 hover:opacity-10'}
        bg-gradient-to-br from-accent to-cyan
      `} />
    </motion.button>
  );
};

export default StyleCard;
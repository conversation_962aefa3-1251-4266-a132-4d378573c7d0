'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface ProgressBarProps {
  value: number; // 0-100
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'aurora' | 'success' | 'warning' | 'error';
  showLabel?: boolean;
  animated?: boolean;
  className?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  size = 'md',
  variant = 'default',
  showLabel = false,
  animated = true,
  className = ''
}) => {
  const percentage = Math.min(100, Math.max(0, (value / max) * 100));

  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };

  const variantClasses = {
    default: 'bg-accent',
    aurora: 'bg-gradient-to-r from-accent via-cyan to-accent',
    success: 'bg-cyan',
    warning: 'bg-ember',
    error: 'bg-crimson'
  };

  return (
    <div className={`w-full ${className}`}>
      {showLabel && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-textSecondary">Progress</span>
          <span className="text-sm text-textPrimary font-medium">
            {Math.round(percentage)}%
          </span>
        </div>
      )}
      
      <div className={`w-full bg-surface rounded-full overflow-hidden ${sizeClasses[size]}`}>
        <motion.div
          className={`
            ${sizeClasses[size]} ${variantClasses[variant]} rounded-full
            transition-all duration-500 ease-out
            ${animated && variant === 'aurora' ? 'animate-pulse' : ''}
          `}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        />
      </div>
    </div>
  );
};

interface GenerationProgressProps {
  stage: 'uploading' | 'processing' | 'complete' | 'error';
  progress: number; // 0-100
  estimatedTime?: number;
  onCancel?: () => void;
}

export const GenerationProgress: React.FC<GenerationProgressProps> = ({
  stage,
  progress,
  estimatedTime,
  onCancel
}) => {
  const getStageInfo = () => {
    switch (stage) {
      case 'uploading':
        return {
          title: 'Uploading Image',
          description: 'Preparing your image for transformation...',
          variant: 'default' as const
        };
      case 'processing':
        return {
          title: 'Casting Spell',
          description: 'AI is weaving dark magic into your image...',
          variant: 'aurora' as const
        };
      case 'complete':
        return {
          title: 'Transformation Complete',
          description: 'Your mystical artwork is ready!',
          variant: 'success' as const
        };
      case 'error':
        return {
          title: 'Spell Failed',
          description: 'Something went wrong during transformation.',
          variant: 'error' as const
        };
    }
  };

  const stageInfo = getStageInfo();

  return (
    <div className="w-full max-w-md mx-auto p-6 glass-card rounded-xl">
      <div className="text-center mb-4">
        <h3 className="text-lg font-semibold text-textPrimary mb-1">
          {stageInfo.title}
        </h3>
        <p className="text-sm text-textSecondary">
          {stageInfo.description}
        </p>
      </div>

      <ProgressBar
        value={progress}
        variant={stageInfo.variant}
        size="lg"
        showLabel
        animated={stage === 'processing'}
        className="mb-4"
      />

      {estimatedTime && stage === 'processing' && (
        <div className="text-center mb-4">
          <p className="text-xs text-textMuted">
            Estimated time remaining: {Math.ceil(estimatedTime / 1000)}s
          </p>
        </div>
      )}

      {onCancel && stage !== 'complete' && stage !== 'error' && (
        <div className="text-center">
          <button
            onClick={onCancel}
            className="text-sm text-textMuted hover:text-textSecondary transition-colors underline"
          >
            Cancel
          </button>
        </div>
      )}
    </div>
  );
};

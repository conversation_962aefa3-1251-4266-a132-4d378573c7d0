# Arcane Artify - Setup Guide

## 🚀 Quick Start

The application is currently running in **development mode** with mock data. To enable full functionality, follow these steps:

## 📋 Prerequisites

1. **Node.js** (v18 or higher)
2. **npm** or **pnpm**
3. **Supabase account** (free tier available)
4. **Google AI Studio account** for Gemini API

## 🔧 Environment Setup

### 1. Copy Environment Variables

```bash
cp .env.example .env.local
```

### 2. Configure Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to **Settings > API** in your Supabase dashboard
3. Copy your project URL and anon key
4. Update `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=your_actual_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 3. Set Up Database

Run the SQL migrations in your Supabase SQL editor:

```sql
-- Create user profiles table
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  username TEXT UNIQUE,
  avatar_url TEXT,
  subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro', 'premium')),
  credits_remaining INTEGER DEFAULT 10,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);

-- Update images table
ALTER TABLE images ADD COLUMN user_id UUID REFERENCES auth.users(id);
ALTER TABLE images ADD COLUMN published BOOLEAN DEFAULT false;
ALTER TABLE images ADD COLUMN metadata JSONB;
ALTER TABLE images ADD COLUMN download_count INTEGER DEFAULT 0;

-- Add RLS for images
ALTER TABLE images ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own images" ON images FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view published images" ON images FOR SELECT USING (published = true);
CREATE POLICY "Users can insert own images" ON images FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own images" ON images FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own images" ON images FOR DELETE USING (auth.uid() = user_id);
```

### 4. Configure Google Gemini API

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add to `.env.local`:

```env
GEMINI_API_KEY=your_gemini_api_key
```

### 5. Set Up Storage (Optional)

1. In Supabase, go to **Storage**
2. Create a new bucket called `images`
3. Set it to public if you want images to be accessible

## 🎯 Features Available

### ✅ Currently Working (Development Mode)
- ✅ Image upload interface
- ✅ Style selection
- ✅ Mock image transformation
- ✅ Gallery with mock data
- ✅ Responsive design
- ✅ Dark magical theme

### 🔧 Requires Configuration
- 🔐 User authentication (Supabase)
- 💾 Real image storage (Supabase Storage)
- 🤖 AI image transformation (Gemini API)
- 📊 User galleries and profiles
- 💳 Subscription management

## 🚀 Production Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy!

### Other Platforms

The app works on any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🛠️ Development Commands

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run tests
npm test

# Run linting
npm run lint
```

## 📚 Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Google AI Studio](https://makersuite.google.com/)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Framer Motion](https://www.framer.com/motion/)

## 🐛 Troubleshooting

### Common Issues

1. **"Multiple GoTrueClient instances"** - This is normal in development mode
2. **Images not loading** - Check your Supabase storage configuration
3. **Authentication not working** - Verify your environment variables
4. **API errors** - Check your Gemini API key and quota

### Getting Help

- Check the browser console for detailed error messages
- Verify all environment variables are set correctly
- Ensure your Supabase project is properly configured
- Test your API keys in their respective dashboards

## 🎨 Customization

The app uses a design system with CSS custom properties. You can customize:

- Colors in `tailwind.config.mjs`
- Fonts in `src/app/globals.css`
- Animations in component files
- Styles in the `ALLOWED_STYLES` array

Enjoy building with Arcane Artify! ✨🎭

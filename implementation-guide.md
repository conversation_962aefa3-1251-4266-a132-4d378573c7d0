# Arcane Artify - Implementation Guide

## 🚀 Technical Requirements

### Dependencies to Add
```json
{
  "dependencies": {
    "@supabase/auth-ui-react": "^0.4.7",
    "@supabase/auth-ui-shared": "^0.1.8",
    "@headlessui/react": "^1.7.17",
    "@heroicons/react": "^2.0.18",
    "react-hot-toast": "^2.4.1",
    "react-intersection-observer": "^9.5.3",
    "clsx": "^2.0.0",
    "class-variance-authority": "^0.7.0",
    "cmdk": "^0.2.0",
    "sonner": "^1.4.0"
  },
  "devDependencies": {
    "@testing-library/react": "^14.1.2",
    "@testing-library/jest-dom": "^6.1.5",
    "@testing-library/user-event": "^14.5.1",
    "msw": "^2.0.11"
  }
}
```

### Environment Variables
```bash
# Required for production
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
GEMINI_API_KEY=your_gemini_api_key

# Optional for enhanced features
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_key
STRIPE_SECRET_KEY=your_stripe_secret
NEXT_PUBLIC_PLAUSIBLE_DOMAIN=your_domain
SENTRY_DSN=your_sentry_dsn
```

## 🗄️ Database Schema Updates

### Supabase Schema Extensions
```sql
-- Add user profiles table
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  username TEXT UNIQUE,
  avatar_url TEXT,
  subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro', 'premium')),
  credits_remaining INTEGER DEFAULT 10,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);

-- Update images table
ALTER TABLE images ADD COLUMN user_id UUID REFERENCES auth.users(id);
ALTER TABLE images ADD COLUMN published BOOLEAN DEFAULT false;
ALTER TABLE images ADD COLUMN metadata JSONB;
ALTER TABLE images ADD COLUMN download_count INTEGER DEFAULT 0;

-- Add RLS for images
ALTER TABLE images ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own images" ON images FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view published images" ON images FOR SELECT USING (published = true);
CREATE POLICY "Users can insert own images" ON images FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own images" ON images FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own images" ON images FOR DELETE USING (auth.uid() = user_id);

-- Add usage tracking
CREATE TABLE usage_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  action TEXT NOT NULL,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🔐 Authentication Implementation

### Supabase Auth Setup
```typescript
// lib/auth.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { AuthError, User } from '@supabase/supabase-js';

export const supabase = createClientComponentClient();

export interface AuthState {
  user: User | null;
  loading: boolean;
  error: AuthError | null;
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null
  });

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session }, error }) => {
      setAuthState({
        user: session?.user ?? null,
        loading: false,
        error
      });
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setAuthState({
          user: session?.user ?? null,
          loading: false,
          error: null
        });
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  return authState;
};
```

### Magic Link Authentication
```typescript
// components/auth/AuthModal.tsx
import { Auth } from '@supabase/auth-ui-react';
import { ThemeSupa } from '@supabase/auth-ui-shared';

const AuthModal = ({ isOpen, onClose }: AuthModalProps) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <div className="p-6">
        <h2 className="text-2xl font-arcane font-bold text-center mb-6">
          Enter the Arcane Realm
        </h2>
        
        <Auth
          supabaseClient={supabase}
          appearance={{
            theme: ThemeSupa,
            variables: {
              default: {
                colors: {
                  brand: '#7e3ff2',
                  brandAccent: '#38b2ac',
                  inputBackground: 'rgba(255, 255, 255, 0.05)',
                  inputBorder: 'rgba(255, 255, 255, 0.1)',
                  inputText: '#ede9fe',
                }
              }
            },
            className: {
              container: 'auth-container',
              button: 'auth-button',
              input: 'auth-input',
            }
          }}
          providers={['google', 'github']}
          redirectTo={`${window.location.origin}/auth/callback`}
          onlyThirdPartyProviders={false}
          magicLink={true}
          view="magic_link"
        />
        
        <div className="mt-4 text-center">
          <button
            onClick={onClose}
            className="text-cyan-400 hover:text-cyan-300 text-sm underline"
          >
            Continue as Guest
          </button>
        </div>
      </div>
    </Modal>
  );
};
```

## 🎨 Component Implementation Examples

### Spell Circle Upload Zone
```typescript
// components/upload/SpellCircleUpload.tsx
const SpellCircleUpload = ({ onFileSelect, selectedFile, disabled }: SpellCircleUploadProps) => {
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: (files) => onFileSelect(files[0]),
    accept: { 'image/*': ['.png', '.jpg', '.jpeg', '.webp'] },
    maxSize: 10 * 1024 * 1024,
    multiple: false,
    disabled
  });

  return (
    <div className="flex justify-center">
      <motion.div
        {...getRootProps()}
        className={`
          relative w-64 h-64 rounded-full border-4 border-dashed
          flex items-center justify-center cursor-pointer
          transition-all duration-300 ease-out
          ${isDragActive 
            ? 'border-accent bg-accent/10 scale-105' 
            : 'border-glassBorder hover:border-cyan hover:scale-102'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        whileHover={{ scale: disabled ? 1 : 1.02 }}
        whileTap={{ scale: disabled ? 1 : 0.98 }}
      >
        <input {...getInputProps()} aria-label="Upload image file" />
        
        {/* Animated rune border */}
        <div className="absolute inset-0 rounded-full">
          <svg className="w-full h-full animate-spin-slow" viewBox="0 0 100 100">
            <circle
              cx="50"
              cy="50"
              r="48"
              fill="none"
              stroke="url(#runeGradient)"
              strokeWidth="1"
              strokeDasharray="10 5"
              opacity="0.6"
            />
            <defs>
              <linearGradient id="runeGradient">
                <stop offset="0%" stopColor="#7e3ff2" />
                <stop offset="50%" stopColor="#38b2ac" />
                <stop offset="100%" stopColor="#7e3ff2" />
              </linearGradient>
            </defs>
          </svg>
        </div>

        <div className="text-center z-10">
          {selectedFile ? (
            <div>
              <CheckCircleIcon className="w-12 h-12 text-cyan mx-auto mb-2" />
              <p className="text-sm text-textPrimary font-medium">
                {selectedFile.name}
              </p>
            </div>
          ) : (
            <div>
              <CloudArrowUpIcon className="w-12 h-12 text-textMuted mx-auto mb-2" />
              <p className="text-sm text-textSecondary">
                Drop your image here
              </p>
              <p className="text-xs text-textMuted mt-1">
                or click to browse
              </p>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};
```

### Enhanced Gallery Card
```typescript
// components/gallery/GalleryCard.tsx
const GalleryCard = ({ image, onTogglePublish, onDelete, onDownload }: GalleryCardProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  return (
    <motion.div
      className="relative group rounded-lg overflow-hidden bg-surface"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ y: -4 }}
      layout
    >
      {/* Image */}
      <div className="relative aspect-square">
        {!imageLoaded && (
          <div className="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900 animate-pulse" />
        )}
        
        <Image
          src={image.url}
          alt={`Transformed with ${image.style} style`}
          fill
          className={`object-cover transition-opacity duration-300 ${
            imageLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={() => setImageLoaded(true)}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />

        {/* Privacy indicator */}
        <div className="absolute top-2 left-2">
          {image.published ? (
            <div className="flex items-center gap-1 px-2 py-1 bg-cyan/20 rounded-full text-xs text-cyan">
              <GlobeAltIcon className="w-3 h-3" />
              <span>Public</span>
            </div>
          ) : (
            <div className="flex items-center gap-1 px-2 py-1 bg-gray-800/80 rounded-full text-xs text-textMuted">
              <LockClosedIcon className="w-3 h-3" />
              <span>Private</span>
            </div>
          )}
        </div>

        {/* Hover overlay */}
        <AnimatePresence>
          {isHovered && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center"
            >
              <div className="flex gap-2">
                <IconButton
                  icon={ArrowDownTrayIcon}
                  onClick={() => onDownload(image.id)}
                  tooltip="Download"
                  variant="glass"
                />
                <IconButton
                  icon={image.published ? EyeSlashIcon : EyeIcon}
                  onClick={() => onTogglePublish(image.id)}
                  tooltip={image.published ? 'Make Private' : 'Make Public'}
                  variant="glass"
                />
                <IconButton
                  icon={TrashIcon}
                  onClick={() => onDelete(image.id)}
                  tooltip="Delete"
                  variant="destructive"
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Metadata */}
      <div className="p-3">
        <p className="text-sm font-medium text-textPrimary truncate">
          {image.style}
        </p>
        <p className="text-xs text-textMuted">
          {formatDistanceToNow(new Date(image.createdAt), { addSuffix: true })}
        </p>
      </div>
    </motion.div>
  );
};
```

## 🧪 Testing Strategy

### Component Testing
```typescript
// __tests__/components/SpellCircleUpload.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SpellCircleUpload } from '@/components/upload/SpellCircleUpload';

describe('SpellCircleUpload', () => {
  const mockOnFileSelect = jest.fn();

  beforeEach(() => {
    mockOnFileSelect.mockClear();
  });

  it('renders upload zone with correct accessibility attributes', () => {
    render(<SpellCircleUpload onFileSelect={mockOnFileSelect} />);
    
    const uploadZone = screen.getByLabelText('Upload image file');
    expect(uploadZone).toBeInTheDocument();
    expect(uploadZone).toHaveAttribute('type', 'file');
  });

  it('handles file selection correctly', async () => {
    const user = userEvent.setup();
    render(<SpellCircleUpload onFileSelect={mockOnFileSelect} />);
    
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const input = screen.getByLabelText('Upload image file');
    
    await user.upload(input, file);
    
    expect(mockOnFileSelect).toHaveBeenCalledWith(file);
  });

  it('shows disabled state correctly', () => {
    render(<SpellCircleUpload onFileSelect={mockOnFileSelect} disabled />);
    
    const uploadZone = screen.getByLabelText('Upload image file');
    expect(uploadZone).toBeDisabled();
  });
});
```

### API Route Testing
```typescript
// __tests__/api/generate.test.ts
import { createMocks } from 'node-mocks-http';
import handler from '@/pages/api/generate';

describe('/api/generate', () => {
  it('requires authentication', async () => {
    const { req, res } = createMocks({ method: 'POST' });
    
    await handler(req, res);
    
    expect(res._getStatusCode()).toBe(401);
  });

  it('validates file upload', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      headers: { authorization: 'Bearer valid-token' },
      body: { style: 'Mystic Veil' } // Missing image
    });
    
    await handler(req, res);
    
    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toMatchObject({
      error: expect.stringContaining('image')
    });
  });
});
```

This implementation guide provides the technical foundation for building the enhanced Arcane Artify application with proper authentication, security, and user experience improvements.

"use client";
import React from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, RefreshCw, Clock } from 'lucide-react';
import { AppError, ErrorType, formatTimeRemaining } from '../lib/errors';

interface ErrorDisplayProps {
  error: AppError;
  onRetry?: () => void;
  className?: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error, onRetry, className = '' }) => {
  const getErrorIcon = () => {
    switch (error.type) {
      case ErrorType.RATE_LIMIT_ERROR:
        return <Clock className="w-5 h-5" />;
      case ErrorType.NETWORK_ERROR:
        return <RefreshCw className="w-5 h-5" />;
      default:
        return <AlertCircle className="w-5 h-5" />;
    }
  };

  const getErrorColor = () => {
    switch (error.type) {
      case ErrorType.RATE_LIMIT_ERROR:
        return 'text-yellow-500 border-yellow-500 bg-yellow-500/10';
      case ErrorType.NETWORK_ERROR:
        return 'text-blue-500 border-blue-500 bg-blue-500/10';
      case ErrorType.VALIDATION_ERROR:
        return 'text-orange-500 border-orange-500 bg-orange-500/10';
      default:
        return 'text-red-500 border-red-500 bg-red-500/10';
    }
  };

  const showRetryButton = error.retryable && onRetry && error.type !== ErrorType.RATE_LIMIT_ERROR;
  const showTimeRemaining = error.type === ErrorType.RATE_LIMIT_ERROR && error.resetTime;

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={`border rounded-lg p-4 ${getErrorColor()} ${className}`}
    >
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-0.5">
          {getErrorIcon()}
        </div>
        <div className="flex-1 min-w-0">
          <p className="font-medium">{error.message}</p>
          {error.details && (
            <p className="text-sm opacity-75 mt-1">{error.details}</p>
          )}
          {showTimeRemaining && (
            <p className="text-sm opacity-75 mt-1">
              Try again in {formatTimeRemaining(error.resetTime!)}
            </p>
          )}
          {showRetryButton && (
            <button
              onClick={onRetry}
              className="mt-3 inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-md bg-current/10 hover:bg-current/20 transition-colors duration-200"
            >
              <RefreshCw className="w-4 h-4 mr-1.5" />
              Try Again
            </button>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default ErrorDisplay;

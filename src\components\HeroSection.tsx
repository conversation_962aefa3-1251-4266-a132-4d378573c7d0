"use client";
import React from 'react';
import { motion } from 'framer-motion';
import { SparklesIcon, ShieldCheckIcon, CpuChipIcon } from '@heroicons/react/24/outline';

const HeroSection: React.FC = () => {
  const scrollToUpload = () => {
    const uploadSection = document.querySelector('#upload-section');
    uploadSection?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center text-center overflow-hidden">
      {/* Animated background */}
      <div className="absolute inset-0 z-0">
        <div className="w-full h-full bg-gradient-to-br from-base via-surface to-base">
          {/* Floating orbs */}
          <motion.div
            animate={{
              x: [0, 100, 0],
              y: [0, -100, 0],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute top-1/4 left-1/4 w-32 h-32 bg-accent/20 rounded-full blur-xl"
          />
          <motion.div
            animate={{
              x: [0, -150, 0],
              y: [0, 100, 0],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute top-3/4 right-1/4 w-48 h-48 bg-cyan/20 rounded-full blur-xl"
          />
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          {/* Logo/Icon */}
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ duration: 1, delay: 0.2 }}
            className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-accent to-cyan rounded-2xl mb-8 shadow-glow"
          >
            <SparklesIcon className="w-10 h-10 text-white" />
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-5xl sm:text-6xl lg:text-7xl font-arcane font-bold mb-6 leading-tight"
          >
            <span className="bg-gradient-to-r from-accent via-cyan to-accent bg-clip-text text-transparent">
              Arcane Artify
            </span>
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-xl sm:text-2xl text-textSecondary mb-8 max-w-3xl mx-auto leading-relaxed"
          >
            Transform your images with mystical AI-powered artistic styles.
            Create stunning dark-magical artwork with our advanced image generation technology.
          </motion.p>

          <motion.button
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            onClick={scrollToUpload}
            className="btn-primary text-lg px-8 py-4 rounded-2xl shadow-glow hover:scale-105 active:scale-95 transition-all duration-300"
          >
            <SparklesIcon className="w-5 h-5 mr-2" />
            Begin Your Ritual
          </motion.button>
        </motion.div>

        {/* Trust indicators */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="flex flex-wrap justify-center items-center gap-8 text-textMuted"
        >
          <div className="flex items-center space-x-2">
            <CpuChipIcon className="w-5 h-5 text-accent" />
            <span className="text-sm font-medium">Powered by Gemini 2.0</span>
          </div>
          <div className="flex items-center space-x-2">
            <ShieldCheckIcon className="w-5 h-5 text-cyan" />
            <span className="text-sm font-medium">Supabase Secure</span>
          </div>
          <div className="flex items-center space-x-2">
            <SparklesIcon className="w-5 h-5 text-ember" />
            <span className="text-sm font-medium">15 Mystical Styles</span>
          </div>
        </motion.div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-textMuted rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-textMuted rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  );
};

export default HeroSection;
"use client";
import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion } from 'framer-motion';
import { ImageUp } from 'lucide-react';
import Image from 'next/image';
import { validateImageFile } from '../lib/validation';
import { ErrorType, AppError } from '../lib/errors';

interface ImageUploadSectionProps {
  selectedFile: File | null;
  onFileSelect: (file: File | null) => void;
  onError: (error: AppError) => void;
}

const ImageUploadSection: React.FC<ImageUploadSectionProps> = ({
  selectedFile,
  onFileSelect,
  onError
}) => {
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      // Validate the file before setting it
      const validation = validateImageFile(file);
      if (!validation.isValid) {
        onError({
          type: ErrorType.VALIDATION_ERROR,
          message: 'Invalid file selected',
          details: validation.errors.join(', '),
          retryable: false
        });
        return;
      }
    }
    
    onFileSelect(file);
  }, [onFileSelect, onError]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/webp': ['.webp'],
    },
    multiple: false,
    maxSize: 10 * 1024 * 1024, // 10MB
    minSize: 1024, // 1KB
  });

  return (
    <motion.div
      initial={{ opacity: 0, x: -50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6 }}
      className="bg-gray-800 p-6 rounded-lg"
    >
      <h3 className="text-2xl font-semibold mb-4">Upload Image</h3>
      <div
        {...getRootProps()}
        className={`border-2 border-dashed p-12 rounded-lg text-center cursor-pointer transition-colors duration-200 ${
          isDragActive ? 'border-arcane-primary bg-gray-700' : 'border-gray-600 hover:border-gray-400'
        }`}
      >
        <input {...getInputProps()} />
        <ImageUp className="mx-auto w-16 h-16 text-gray-400 mb-4" />
        {selectedFile ? (
          <p className="text-lg text-arcane-light">Selected: {selectedFile.name}</p>
        ) : (
          <p className="text-lg text-gray-400">
            Drag &apos;n&apos; drop an image here, or click to select one
          </p>
        )}
      </div>
      {selectedFile && (
        <div className="mt-4 text-center">
          <Image 
            src={URL.createObjectURL(selectedFile)} 
            alt="Selected" 
            width={500} 
            height={500} 
            className="max-w-full h-auto rounded-lg mx-auto" 
          />
        </div>
      )}
    </motion.div>
  );
};

export default ImageUploadSection;

"use client";
import React from 'react';
import { motion } from 'framer-motion';
import StyleCard from './StyleCard';
import { ALLOWED_STYLES } from '../lib/validation';

interface StyleSelectionSectionProps {
  selectedStyle: string | null;
  onStyleSelect: (style: string) => void;
}

const StyleSelectionSection: React.FC<StyleSelectionSectionProps> = ({
  selectedStyle,
  onStyleSelect
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      className="bg-gray-800 p-6 rounded-lg"
    >
      <h3 className="text-2xl font-semibold mb-4">Choose Style</h3>
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto pr-2">
        {ALLOWED_STYLES.map((style) => (
          <StyleCard
            key={style}
            styleName={style}
            isSelected={selectedStyle === style}
            onSelect={onStyleSelect}
          />
        ))}
      </div>
    </motion.div>
  );
};

export default StyleSelectionSection;

"use client";
import React from 'react';
import { motion } from 'framer-motion';
import StyleCard from './StyleCard';
import { ALLOWED_STYLES } from '../lib/validation';

interface StyleSelectionSectionProps {
  selectedStyle: string | null;
  onStyleSelect: (style: string) => void;
}

const StyleSelectionSection: React.FC<StyleSelectionSectionProps> = ({
  selectedStyle,
  onStyleSelect
}) => {
  return (
    <div className="glass-card p-6 rounded-2xl">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
        {ALLOWED_STYLES.map((style, index) => (
          <motion.div
            key={style}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
          >
            <StyleCard
              styleName={style}
              isSelected={selectedStyle === style}
              onSelect={onStyleSelect}
            />
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export { StyleSelectionSection };
export default StyleSelectionSection;

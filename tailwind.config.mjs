/** @type {import('tailwindcss').Config} */
export default {
  darkMode: 'class',
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Enhanced design tokens
        base: '#0b0b12',
        surface: '#1a1a2e',
        accent: '#7e3ff2',
        cyan: '#38b2ac',
        ember: '#d97706',
        crimson: '#dc2626',

        // Glass effects
        glass: 'rgba(255, 255, 255, 0.05)',
        glassBorder: 'rgba(255, 255, 255, 0.1)',

        // Text hierarchy
        textPrimary: '#ede9fe',
        textSecondary: '#a78bfa',
        textMuted: '#6b7280',

        // Legacy arcane colors (for backward compatibility)
        arcane: {
          primary: '#7e3ff2',
          secondary: '#a78bfa',
          dark: '#1e1b4b',
          accent: '#c084fc',
          light: '#ede9fe',
        },
      },
      fontFamily: {
        arcane: ['"Cinzel Decorative"', 'serif'],
        body: ['Manrope', 'sans-serif'],
      },
      boxShadow: {
        arcane: '0 4px 14px 0 rgba(126, 63, 242, 0.39)',
        glow: '0 0 20px rgba(126, 63, 242, 0.5)',
        glass: '0 8px 32px rgba(0, 0, 0, 0.3)',
      },
      backdropBlur: {
        xs: '2px',
      },
      animation: {
        'spin-slow': 'spin 8s linear infinite',
        'pulse-glow': 'pulseGlow 2s ease-in-out infinite',
        'fade-in': 'fadeIn 0.3s ease-out',
        'slide-up': 'slideUp 0.5s cubic-bezier(0.34, 1.56, 0.64, 1)',
        'scale-in': 'scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)',
      },
      keyframes: {
        pulseGlow: {
          '0%, 100%': { boxShadow: '0 0 20px rgba(126, 63, 242, 0.3)' },
          '50%': { boxShadow: '0 0 40px rgba(126, 63, 242, 0.6)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        scaleIn: {
          '0%': { opacity: '0', transform: 'scale(0.9)' },
          '100%': { opacity: '1', transform: 'scale(1)' },
        },
      },
    },
  },
  plugins: [],
}
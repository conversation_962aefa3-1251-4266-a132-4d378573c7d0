"use client";
import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';

interface TransformationResultProps {
  transformedImage: string;
  style: string;
}

const TransformationResult: React.FC<TransformationResultProps> = ({
  transformedImage,
  style
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="mt-8"
    >
      <h3 className="text-2xl font-semibold mb-4 text-center">Transformed Image</h3>
      <div className="relative">
        <Image 
          src={transformedImage} 
          alt={`Transformed with ${style} style`}
          width={500} 
          height={500} 
          className="max-w-full h-auto rounded-lg mx-auto shadow-lg" 
        />
        <div className="absolute top-2 right-2 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
          {style}
        </div>
      </div>
    </motion.div>
  );
};

export default TransformationResult;

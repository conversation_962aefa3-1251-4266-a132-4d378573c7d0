"use client";
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  ArrowDownTrayIcon,
  ShareIcon,
  EyeIcon,
  EyeSlashIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import Image from 'next/image';
import { useAuth } from '@/lib/auth';

interface TransformationResultProps {
  transformedImage: string;
  style: string;
}

const TransformationResult: React.FC<TransformationResultProps> = ({
  transformedImage,
  style
}) => {
  const { user } = useAuth();
  const [isPublic, setIsPublic] = useState(false);

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = transformedImage;
    link.download = `arcane-artify-${style.toLowerCase().replace(/\s+/g, '-')}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Arcane Artify - ${style}`,
          text: `Check out my mystical transformation using ${style} style!`,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  const handleTogglePublic = () => {
    setIsPublic(!isPublic);
    // TODO: Implement actual privacy toggle with API call
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="mt-16"
    >
      <div className="text-center mb-8">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-accent to-cyan rounded-full mb-4"
        >
          <SparklesIcon className="w-8 h-8 text-white" />
        </motion.div>
        <h3 className="text-3xl font-arcane font-bold text-textPrimary mb-2">
          Transformation Complete!
        </h3>
        <p className="text-textSecondary">
          Your image has been transformed with <span className="text-accent font-medium">{style}</span> style
        </p>
      </div>

      <div className="max-w-2xl mx-auto">
        <div className="glass-card p-6 rounded-2xl">
          <div className="relative group">
            <Image
              src={transformedImage}
              alt={`Transformed with ${style} style`}
              width={800}
              height={800}
              className="w-full h-auto rounded-xl shadow-lg"
              priority
            />

            {/* Style badge */}
            <div className="absolute top-4 left-4 bg-black/70 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium">
              {style}
            </div>

            {/* Action buttons overlay */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100">
              <div className="flex space-x-3">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleDownload}
                  className="p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors"
                  title="Download image"
                >
                  <ArrowDownTrayIcon className="w-5 h-5" />
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleShare}
                  className="p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors"
                  title="Share image"
                >
                  <ShareIcon className="w-5 h-5" />
                </motion.button>

                {user && (
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={handleTogglePublic}
                    className="p-3 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors"
                    title={isPublic ? "Make private" : "Make public"}
                  >
                    {isPublic ? (
                      <EyeIcon className="w-5 h-5" />
                    ) : (
                      <EyeSlashIcon className="w-5 h-5" />
                    )}
                  </motion.button>
                )}
              </div>
            </div>
          </div>

          {/* Action buttons for mobile */}
          <div className="mt-6 flex flex-wrap justify-center gap-3 md:hidden">
            <button
              onClick={handleDownload}
              className="btn-secondary text-sm px-4 py-2 flex items-center"
            >
              <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
              Download
            </button>

            <button
              onClick={handleShare}
              className="btn-secondary text-sm px-4 py-2 flex items-center"
            >
              <ShareIcon className="w-4 h-4 mr-2" />
              Share
            </button>

            {user && (
              <button
                onClick={handleTogglePublic}
                className="btn-secondary text-sm px-4 py-2 flex items-center"
              >
                {isPublic ? (
                  <>
                    <EyeIcon className="w-4 h-4 mr-2" />
                    Public
                  </>
                ) : (
                  <>
                    <EyeSlashIcon className="w-4 h-4 mr-2" />
                    Private
                  </>
                )}
              </button>
            )}
          </div>

          {!user && (
            <div className="mt-6 p-4 bg-cyan/10 border border-cyan/20 rounded-xl text-center">
              <p className="text-sm text-textSecondary">
                <span className="text-cyan font-medium">Sign in</span> to save this transformation to your private gallery
              </p>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export { TransformationResult };
export default TransformationResult;

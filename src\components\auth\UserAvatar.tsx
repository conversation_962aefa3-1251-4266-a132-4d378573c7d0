'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  UserCircleIcon, 
  Cog6ToothIcon, 
  ArrowRightOnRectangleIcon,
  PhotoIcon,
  CreditCardIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '@/lib/auth';
import Image from 'next/image';

interface UserAvatarProps {
  size?: 'sm' | 'md' | 'lg';
  showDropdown?: boolean;
}

export const UserAvatar: React.FC<UserAvatarProps> = ({
  size = 'md',
  showDropdown = true
}) => {
  const { user, signOut } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      setIsOpen(false);
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  if (!user) return null;

  const avatarUrl = user.user_metadata?.avatar_url;
  const displayName = user.user_metadata?.full_name || user.email?.split('@')[0] || 'User';
  const email = user.email;

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => showDropdown && setIsOpen(!isOpen)}
        className={`
          ${sizeClasses[size]} rounded-full overflow-hidden
          ring-2 ring-glassBorder hover:ring-cyan transition-all duration-200
          focus:outline-none focus:ring-2 focus:ring-cyan focus:ring-offset-2 focus:ring-offset-base
          ${showDropdown ? 'cursor-pointer' : 'cursor-default'}
        `}
        aria-label="User menu"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {avatarUrl ? (
          <Image
            src={avatarUrl}
            alt={displayName}
            width={48}
            height={48}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-accent to-cyan flex items-center justify-center">
            <UserCircleIcon className="w-2/3 h-2/3 text-white" />
          </div>
        )}
      </button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && showDropdown && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            className="absolute right-0 mt-2 w-64 glass-card rounded-xl shadow-lg z-50"
          >
            {/* User Info */}
            <div className="p-4 border-b border-glassBorder">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full overflow-hidden ring-2 ring-glassBorder">
                  {avatarUrl ? (
                    <Image
                      src={avatarUrl}
                      alt={displayName}
                      width={40}
                      height={40}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-accent to-cyan flex items-center justify-center">
                      <UserCircleIcon className="w-6 h-6 text-white" />
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-textPrimary truncate">
                    {displayName}
                  </p>
                  <p className="text-xs text-textMuted truncate">
                    {email}
                  </p>
                </div>
              </div>
            </div>

            {/* Menu Items */}
            <div className="py-2">
              <DropdownItem
                icon={PhotoIcon}
                label="My Gallery"
                onClick={() => {
                  setIsOpen(false);
                  // Navigate to gallery
                  window.location.href = '/gallery';
                }}
              />
              <DropdownItem
                icon={CreditCardIcon}
                label="Subscription"
                onClick={() => {
                  setIsOpen(false);
                  // Navigate to subscription
                  window.location.href = '/pricing';
                }}
              />
              <DropdownItem
                icon={Cog6ToothIcon}
                label="Settings"
                onClick={() => {
                  setIsOpen(false);
                  // Navigate to settings
                  window.location.href = '/settings';
                }}
              />
            </div>

            {/* Sign Out */}
            <div className="border-t border-glassBorder py-2">
              <DropdownItem
                icon={ArrowRightOnRectangleIcon}
                label="Sign Out"
                onClick={handleSignOut}
                variant="danger"
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

interface DropdownItemProps {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  onClick: () => void;
  variant?: 'default' | 'danger';
}

const DropdownItem: React.FC<DropdownItemProps> = ({
  icon: Icon,
  label,
  onClick,
  variant = 'default'
}) => {
  const baseClasses = "w-full flex items-center px-4 py-2 text-sm transition-colors duration-200 focus:outline-none";
  const variantClasses = {
    default: "text-textSecondary hover:text-textPrimary hover:bg-glass",
    danger: "text-crimson hover:text-crimson hover:bg-crimson/10"
  };

  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${variantClasses[variant]}`}
    >
      <Icon className="w-4 h-4 mr-3" />
      {label}
    </button>
  );
};

# Arcane Artify - UI/UX Design Specification v2.0

## 🎯 Design Goals
- Preserve dark-magical aesthetic while addressing security/auth gaps
- Production-ready React 19 + Tailwind 4 components
- WCAG 2.2-AA accessibility compliance
- Seamless authentication flow with private galleries

## 🎨 Enhanced Design System

### Color Tokens (Updated)
```typescript
const tokens = {
  // Base colors (existing + new)
  base: '#0b0b12',           // Body backgrounds
  surface: '#1a1a2e',        // Card backgrounds  
  accent: '#7e3ff2',         // Primary actions (updated from #5e2aff)
  cyan: '#38b2ac',           // Links, borders, success states
  ember: '#d97706',          // Warnings, notifications
  crimson: '#dc2626',        // Errors, destructive actions
  
  // Glassmorphic overlays
  glass: 'rgba(255, 255, 255, 0.05)',
  glassBorder: 'rgba(255, 255, 255, 0.1)',
  
  // Text hierarchy
  textPrimary: '#ede9fe',    // Headings
  textSecondary: '#a78bfa',  // Body text
  textMuted: '#6b7280',      // Captions, placeholders
}
```

### Typography Scale
```typescript
const typography = {
  // Fonts
  heading: '<PERSON><PERSON><PERSON> Decorative, serif',
  body: 'Manrope, sans-serif',
  
  // Scale
  'text-xs': '0.75rem',      // 12px - Captions
  'text-sm': '0.875rem',     // 14px - Small text
  'text-base': '1rem',       // 16px - Body
  'text-lg': '1.125rem',     // 18px - Large body
  'text-xl': '1.25rem',      // 20px - Subheadings
  'text-2xl': '1.5rem',      // 24px - Card titles
  'text-3xl': '1.875rem',    // 30px - Section headers
  'text-4xl': '2.25rem',     // 36px - Page titles
  'text-5xl': '3rem',        // 48px - Hero headlines
}
```

### Spacing & Layout
```typescript
const spacing = {
  grid: '8px',               // Base grid unit
  radius: {
    sm: '0.375rem',          // 6px - Small elements
    md: '0.5rem',            // 8px - Buttons, inputs
    lg: '0.75rem',           // 12px - Cards
    xl: '1rem',              // 16px - Modals
    '2xl': '1.5rem',         // 24px - Hero sections
  },
  shadow: {
    sm: '0 1px 2px rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.2)',
    arcane: '0 4px 14px rgba(126, 63, 242, 0.39)',
    glow: '0 0 20px rgba(126, 63, 242, 0.5)',
  }
}
```

### Motion System
```typescript
const motion = {
  // Easing curves
  easeOut: 'cubic-bezier(0.16, 1, 0.3, 1)',
  easeIn: 'cubic-bezier(0.4, 0, 0.6, 1)',
  spring: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
  
  // Durations
  fast: '150ms',
  normal: '300ms',
  slow: '500ms',
  
  // Spring physics
  damping: 20,
  stiffness: 150,
}
```

## 📱 Component Specifications

### 1. Authentication Modal
```typescript
interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'signin' | 'signup';
}

// Visual Design:
// - Backdrop: bg-black/60 backdrop-blur-sm
// - Modal: bg-surface border border-glassBorder rounded-2xl
// - Tabs: Segmented control with accent highlight
// - Form: Glass inputs with cyan focus rings
// - CTA: Gradient button accent → cyan
// - Guest link: text-cyan-400 underline
```

### 2. Enhanced Navigation
```typescript
interface NavigationProps {
  user?: User | null;
  onAuthOpen: () => void;
  onSignOut: () => void;
}

// Features:
// - Logo: Animated rune icon + wordmark
// - Auth state: Avatar dropdown vs Sign In button
// - Mobile: Hamburger → slide-out drawer
// - Breadcrumbs: For deep navigation
// - Search: Global image search (future)
```

### 3. Spell-Circle Upload Zone
```typescript
interface SpellCircleUploadProps {
  onFileSelect: (file: File) => void;
  selectedFile?: File | null;
  disabled?: boolean;
}

// Visual Design:
// - Circular dropzone with animated rune border
// - Hover: Pulsing glow effect
// - Active: Rotating border animation
// - Focus: Cyan ring for keyboard users
// - Error: Crimson border with shake animation
```

### 4. Style Picker Grid
```typescript
interface StylePickerProps {
  styles: StyleOption[];
  selected?: string;
  onSelect: (styleId: string) => void;
}

interface StyleOption {
  id: string;
  name: string;
  preview?: string;
  description?: string;
}

// Visual Design:
// - Glassmorphic cards with preview images
// - Hover: Scale 1.05 + glow shadow
// - Selected: Accent border + checkmark overlay
// - Keyboard: Arrow navigation + Enter to select
// - Loading: Skeleton placeholders
```

### 5. Generation Progress
```typescript
interface GenerationProgressProps {
  stage: 'uploading' | 'processing' | 'complete';
  progress: number; // 0-100
  estimatedTime?: number;
}

// Visual Design:
// - Aurora gradient progress bar
// - Stage indicators with icons
// - Estimated time countdown
// - Cancel button (if supported)
// - Success animation on completion
```

### 6. Gallery Card Enhanced
```typescript
interface GalleryCardProps {
  image: {
    id: string;
    url: string;
    style: string;
    createdAt: Date;
    published: boolean;
  };
  onTogglePublish: (id: string) => void;
  onDelete: (id: string) => void;
  onDownload: (id: string) => void;
}

// Visual Design:
// - Aspect ratio preserved with object-cover
// - Hover overlay: Actions toolbar
// - Published indicator: Green dot
// - Private indicator: Lock icon
// - Lazy loading with blur placeholder
```

## 🔐 Authentication Flow

### Sign In Modal
1. **Email Input**: Glass input with validation
2. **Magic Link**: Send → Check email message
3. **Guest Mode**: Continue without account
4. **Social**: Google/GitHub options (future)

### User States
- **Anonymous**: Limited uploads, no gallery
- **Authenticated**: Full features, private gallery
- **Premium**: Higher limits, priority processing

## 📄 Page Layouts

### Landing Page (/)
```typescript
// Structure:
// 1. Hero: Nebula background + rotating rune
// 2. Trust indicators: Gemini, Supabase, security badges
// 3. Style showcase: Animated preview grid
// 4. Upload CTA: "Begin Your Ritual" button
// 5. Footer: Links, social, legal
```

### Gallery (/gallery)
```typescript
// Structure:
// 1. Header: "Mystic Vault" + filter controls
// 2. Filter bar: All | Private | Published
// 3. Masonry grid: Responsive columns
// 4. Empty state: Upload encouragement
// 5. Pagination: Infinite scroll
```

### Pricing (/pricing)
```typescript
// Structure:
// 1. Hero: "Choose Your Path"
// 2. Tier cards: Apprentice | Adept | Archmage
// 3. Feature comparison table
// 4. FAQ section
// 5. CTA: Start free trial
```

### Admin Dashboard (/admin)
```typescript
// Structure:
// 1. Sidebar: Navigation tabs
// 2. Users tab: Table with search/filter
// 3. Images tab: Moderation queue
// 4. Analytics: Usage charts
// 5. Settings: System configuration
```

## ♿ Accessibility Implementation

### Keyboard Navigation
- Tab order: Logical flow through interactive elements
- Focus indicators: Cyan ring with 2px offset
- Skip links: "Skip to main content"
- Arrow keys: Grid navigation for style picker

### Screen Reader Support
- Semantic HTML: Proper heading hierarchy
- ARIA labels: All form controls and buttons
- Live regions: Status updates during upload
- Alt text: Descriptive image alternatives

### Color & Contrast
- Minimum 4.5:1 contrast ratio
- Color-blind friendly: No color-only indicators
- High contrast mode: Respect user preferences
- Focus indicators: Visible in all themes

### Motion Preferences
- Respect `prefers-reduced-motion`
- Essential animations only
- Disable parallax/complex animations
- Provide static alternatives

## 📱 Responsive Breakpoints

```typescript
const breakpoints = {
  sm: '640px',   // Mobile landscape
  md: '768px',   // Tablet portrait
  lg: '1024px',  // Tablet landscape
  xl: '1280px',  // Desktop
  '2xl': '1536px' // Large desktop
}

// Layout adaptations:
// - Mobile: Single column, bottom navigation
// - Tablet: Two columns, side navigation
// - Desktop: Multi-column, full navigation
```

## 🎭 Component Variants

### Button Variants
```typescript
const buttonVariants = {
  primary: 'bg-accent hover:bg-accent/90 text-white',
  secondary: 'bg-surface hover:bg-surface/80 text-textPrimary border border-glassBorder',
  ghost: 'hover:bg-glass text-textSecondary',
  destructive: 'bg-crimson hover:bg-crimson/90 text-white',
}
```

### Input Variants
```typescript
const inputVariants = {
  default: 'bg-glass border border-glassBorder focus:border-cyan focus:ring-cyan/20',
  error: 'bg-glass border border-crimson focus:border-crimson focus:ring-crimson/20',
  success: 'bg-glass border border-cyan focus:border-cyan focus:ring-cyan/20',
}
```

## 🔧 Implementation Notes

### Tailwind 4 Migration
- Use new CSS-first approach
- Leverage container queries
- Implement view transitions
- Optimize for performance

### React 19 Features
- Use new `use()` hook for data fetching
- Implement Server Components where appropriate
- Leverage automatic batching
- Optimize with concurrent features

### Security Considerations
- Sanitize all user inputs
- Implement CSP headers
- Use secure image uploads
- Validate file types strictly

## 🔌 Component Contracts

### Core Component Props
```typescript
// Authentication Components
interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'signin' | 'signup';
  onSuccess?: (user: User) => void;
}

interface UserAvatarProps {
  user: User;
  size?: 'sm' | 'md' | 'lg';
  showDropdown?: boolean;
  onSignOut: () => void;
}

// Upload & Generation
interface SpellCircleUploadProps {
  onFileSelect: (file: File) => void;
  selectedFile?: File | null;
  disabled?: boolean;
  maxSize?: number;
  acceptedTypes?: string[];
  'aria-label'?: string;
}

interface StylePickerProps {
  styles: StyleOption[];
  selected?: string;
  onSelect: (styleId: string) => void;
  columns?: number;
  'aria-label'?: string;
}

interface GenerateButtonProps {
  disabled: boolean;
  loading: boolean;
  onGenerate: () => void;
  progress?: number;
  estimatedTime?: number;
}

// Gallery Components
interface GalleryCardProps {
  image: ImageRecord;
  onTogglePublish: (id: string) => void;
  onDelete: (id: string) => void;
  onDownload: (id: string) => void;
  showActions?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

interface GalleryFilterProps {
  currentFilter: 'all' | 'private' | 'published';
  onFilterChange: (filter: string) => void;
  counts: { all: number; private: number; published: number };
}

// UI Primitives
interface ToastProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  description?: string;
  duration?: number;
  onDismiss: () => void;
}

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
}
```

## 🎨 Visual Design Patterns

### Glassmorphism Implementation
```css
.glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass-input {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.glass-input:focus {
  border-color: #38b2ac;
  box-shadow: 0 0 0 3px rgba(56, 178, 172, 0.2);
}
```

### Animation Patterns
```css
/* Entrance animations */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-up {
  animation: slideUp 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.scale-in {
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

/* Loading states */
.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%, 100% { box-shadow: 0 0 20px rgba(126, 63, 242, 0.3); }
  50% { box-shadow: 0 0 40px rgba(126, 63, 242, 0.6); }
}
```

## 🔒 Security UI Patterns

### Input Sanitization Indicators
```typescript
interface SecureInputProps {
  value: string;
  onChange: (value: string) => void;
  sanitize?: boolean;
  showSanitizationWarning?: boolean;
  maxLength?: number;
  pattern?: RegExp;
}

// Visual indicators for sanitized content
const SanitizationBadge = () => (
  <div className="flex items-center gap-1 text-xs text-cyan-400">
    <ShieldCheckIcon className="w-3 h-3" />
    <span>Content sanitized</span>
  </div>
);
```

### Privacy Controls
```typescript
interface PrivacyToggleProps {
  isPublic: boolean;
  onToggle: (isPublic: boolean) => void;
  disabled?: boolean;
  showLabel?: boolean;
}

// Visual design: Toggle with lock/unlock icons
const PrivacyToggle = ({ isPublic, onToggle, disabled, showLabel }: PrivacyToggleProps) => (
  <div className="flex items-center gap-2">
    <button
      onClick={() => onToggle(!isPublic)}
      disabled={disabled}
      className={`
        relative inline-flex h-6 w-11 items-center rounded-full
        transition-colors focus:outline-none focus:ring-2 focus:ring-cyan-500
        ${isPublic ? 'bg-cyan-600' : 'bg-gray-600'}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
      `}
    >
      <span className={`
        inline-block h-4 w-4 transform rounded-full bg-white transition-transform
        ${isPublic ? 'translate-x-6' : 'translate-x-1'}
      `} />
    </button>
    {showLabel && (
      <span className="text-sm text-textSecondary">
        {isPublic ? 'Public' : 'Private'}
      </span>
    )}
  </div>
);
```

## 📊 Data Visualization

### Usage Analytics (Admin)
```typescript
interface AnalyticsCardProps {
  title: string;
  value: number | string;
  change?: number;
  trend?: 'up' | 'down' | 'neutral';
  icon?: React.ComponentType;
}

interface ChartProps {
  data: Array<{ date: string; value: number }>;
  type: 'line' | 'bar' | 'area';
  color?: string;
  height?: number;
}
```

### Progress Indicators
```typescript
interface ProgressBarProps {
  value: number; // 0-100
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'aurora' | 'success' | 'warning' | 'error';
  showLabel?: boolean;
  animated?: boolean;
}

// Aurora gradient progress bar
const AuroraProgress = ({ value, animated = true }: ProgressBarProps) => (
  <div className="w-full bg-gray-700 rounded-full h-2 overflow-hidden">
    <div
      className={`
        h-full bg-gradient-to-r from-accent via-cyan to-accent
        transition-all duration-500 ease-out
        ${animated ? 'animate-pulse' : ''}
      `}
      style={{ width: `${Math.min(100, Math.max(0, value))}%` }}
    />
  </div>
);
```

## 🎯 Error Handling UI

### Error Boundary Component
```typescript
interface ErrorBoundaryProps {
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  children: React.ReactNode;
}

const ErrorFallback = ({ error, retry }: { error: Error; retry: () => void }) => (
  <div className="flex flex-col items-center justify-center p-8 text-center">
    <div className="w-16 h-16 mb-4 text-crimson">
      <ExclamationTriangleIcon />
    </div>
    <h3 className="text-xl font-semibold text-textPrimary mb-2">
      Something went wrong
    </h3>
    <p className="text-textSecondary mb-4 max-w-md">
      {error.message || 'An unexpected error occurred. Please try again.'}
    </p>
    <button
      onClick={retry}
      className="px-4 py-2 bg-accent hover:bg-accent/90 text-white rounded-lg transition-colors"
    >
      Try Again
    </button>
  </div>
);
```

### Toast Notification System
```typescript
interface ToastContextValue {
  toasts: Toast[];
  addToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: string) => void;
  clearAll: () => void;
}

const ToastContainer = () => (
  <div className="fixed bottom-4 right-4 z-50 space-y-2">
    <AnimatePresence>
      {toasts.map((toast) => (
        <motion.div
          key={toast.id}
          initial={{ opacity: 0, y: 50, scale: 0.3 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, scale: 0.5, transition: { duration: 0.2 } }}
          className={`
            max-w-sm p-4 rounded-lg shadow-lg backdrop-blur-sm
            ${toast.type === 'error' ? 'bg-crimson/90 border-crimson' : ''}
            ${toast.type === 'success' ? 'bg-cyan/90 border-cyan' : ''}
            ${toast.type === 'warning' ? 'bg-ember/90 border-ember' : ''}
          `}
        >
          <ToastContent {...toast} />
        </motion.div>
      ))}
    </AnimatePresence>
  </div>
);
```

This specification provides the foundation for implementing a production-ready, accessible, and secure version of Arcane Artify while maintaining its distinctive dark-magical aesthetic.

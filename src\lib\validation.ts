// Input validation utilities

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp'
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const MIN_FILE_SIZE = 1024; // 1KB

export const ALLOWED_STYLES = [
  'Mystic Veil', 'Shadow Weave', 'Arcane Glyph', 'Eldritch Rune',
  'Celestial Bloom', 'Nebula Shroud', 'Abyssal Echo', 'Lunar Halo',
  'Solar Flare', 'Void Tracer', 'Dream Weaver', 'Phoenix Ember',
  'Frostbite Sigil', 'Thunderstorm Crest', 'Enchanted Vine'
];

export function validateImageFile(file: File): ValidationResult {
  const errors: string[] = [];

  // Check file type
  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
    errors.push(`Invalid file type. Allowed types: ${ALLOWED_IMAGE_TYPES.join(', ')}`);
  }

  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    errors.push(`File too large. Maximum size: ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
  }

  if (file.size < MIN_FILE_SIZE) {
    errors.push(`File too small. Minimum size: ${MIN_FILE_SIZE / 1024}KB`);
  }

  // Check file name for security
  if (file.name.length > 255) {
    errors.push('File name too long');
  }

  // Basic security check for file name
  const dangerousChars = /[<>:"/\\|?*\x00-\x1f]/;
  if (dangerousChars.test(file.name)) {
    errors.push('File name contains invalid characters');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function validateStyle(style: string): ValidationResult {
  const errors: string[] = [];

  if (!style || typeof style !== 'string') {
    errors.push('Style is required');
  } else if (!ALLOWED_STYLES.includes(style)) {
    errors.push('Invalid style selected');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function validateFormData(formData: FormData): ValidationResult {
  const errors: string[] = [];

  const image = formData.get('image') as File;
  const style = formData.get('style') as string;

  if (!image) {
    errors.push('Image file is required');
  } else {
    const imageValidation = validateImageFile(image);
    if (!imageValidation.isValid) {
      errors.push(...imageValidation.errors);
    }
  }

  if (!style) {
    errors.push('Style selection is required');
  } else {
    const styleValidation = validateStyle(style);
    if (!styleValidation.isValid) {
      errors.push(...styleValidation.errors);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Sanitize text input to prevent XSS
export function sanitizeText(text: string): string {
  return text
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

// Validate and sanitize file name
export function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[<>:"/\\|?*\x00-\x1f]/g, '') // Remove dangerous characters
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .substring(0, 255); // Limit length
}

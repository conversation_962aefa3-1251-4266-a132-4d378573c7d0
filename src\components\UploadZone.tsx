"use client";
import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion } from 'framer-motion';
import { ImageUp, Wand2, Loader2 } from 'lucide-react';
import Image from 'next/image';
import StyleCard from './StyleCard';
import ErrorDisplay from './ErrorDisplay';
import { uploadImage, saveImageRecord } from '../lib/supabase';
import { getErrorMessage, parseApiError, AppError, ErrorType } from '../lib/errors';
import { validateImageFile, validateStyle, ALLOWED_STYLES } from '../lib/validation';

// Use the validated styles from the validation module
const styles = ALLOWED_STYLES;

const UploadZone: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedStyle, setSelectedStyle] = useState<string | null>(null);
  const [transformedImage, setTransformedImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<AppError | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      // Validate the file before setting it
      const validation = validateImageFile(file);
      if (!validation.isValid) {
        setError({
          type: ErrorType.VALIDATION_ERROR,
          message: 'Invalid file selected',
          details: validation.errors.join(', '),
          retryable: false
        });
        return;
      }
    }

    setSelectedFile(file);
    setTransformedImage(null);
    setError(null);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/webp': ['.webp'],
    },
    multiple: false,
    maxSize: 10 * 1024 * 1024, // 10MB
    minSize: 1024, // 1KB
  });

  const handleTransform = async () => {
    // Validate inputs
    if (!selectedFile || !selectedStyle) {
      setError({
        type: ErrorType.VALIDATION_ERROR,
        message: 'Please select an image and a style.',
        retryable: false
      });
      return;
    }

    // Double-check file validation
    const fileValidation = validateImageFile(selectedFile);
    if (!fileValidation.isValid) {
      setError({
        type: ErrorType.VALIDATION_ERROR,
        message: 'Invalid file selected',
        details: fileValidation.errors.join(', '),
        retryable: false
      });
      return;
    }

    // Validate style
    const styleValidation = validateStyle(selectedStyle);
    if (!styleValidation.isValid) {
      setError({
        type: ErrorType.VALIDATION_ERROR,
        message: 'Invalid style selected',
        details: styleValidation.errors.join(', '),
        retryable: false
      });
      return;
    }

    setLoading(true);
    setError(null);
    setTransformedImage(null);

    try {
      // Upload original image to Supabase
      const originalImageUrl = await uploadImage(selectedFile);

      const formData = new FormData();
      formData.append('image', selectedFile);
      formData.append('style', selectedStyle);

      const response = await fetch('/api/generate', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const apiError = parseApiError(response, errorData);
        setError(apiError);
        return;
      }

      const data = await response.json();
      const transformedImageUrl = data.image;

      setTransformedImage(transformedImageUrl);

      // Save record to database (using anonymous user for now until auth is implemented)
      try {
        await saveImageRecord(null, originalImageUrl, transformedImageUrl, selectedStyle);
      } catch (dbError) {
        // Don't fail the whole operation if database save fails
        console.warn('Failed to save image record:', dbError);
        const dbErrorInfo = getErrorMessage(dbError);
        setError(dbErrorInfo);
      }

    } catch (err) {
      const errorInfo = getErrorMessage(err);
      setError(errorInfo);
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = () => {
    handleTransform();
  };

  return (
    <section className="container mx-auto p-8 bg-gray-900 rounded-lg shadow-xl text-arcane-light my-12">
      <h2 className="text-4xl font-arcane font-bold text-center mb-8">Transform Your Art</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Upload Section */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <h3 className="text-2xl font-semibold mb-4">Upload Image</h3>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed p-12 rounded-lg text-center cursor-pointer transition-colors duration-200 ${
              isDragActive ? 'border-arcane-primary bg-gray-700' : 'border-gray-600 hover:border-gray-400'
            }`}
          >
            <input {...getInputProps()} />
            <ImageUp className="mx-auto w-16 h-16 text-gray-400 mb-4" />
            {selectedFile ? (
              <p className="text-lg text-arcane-light">Selected: {selectedFile.name}</p>
            ) : (
              <p className="text-lg text-gray-400">Drag &apos;n&apos; drop an image here, or click to select one</p>
            )}
          </div>
          {selectedFile && (
            <div className="mt-4 text-center">
              <Image src={URL.createObjectURL(selectedFile)} alt="Selected" width={500} height={500} className="max-w-full h-auto rounded-lg mx-auto" />
            </div>
          )}
        </motion.div>

        {/* Style Selection */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-gray-800 p-6 rounded-lg"
        >
          <h3 className="text-2xl font-semibold mb-4">Choose Style</h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto pr-2">
            {styles.map((style) => (
              <StyleCard
                key={style}
                styleName={style}
                isSelected={selectedStyle === style}
                onSelect={setSelectedStyle}
              />
            ))}
          </div>
        </motion.div>
      </div>

      {/* Action Button & Result */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="mt-8 text-center"
      >
        {error && (
          <div className="mb-4">
            <ErrorDisplay error={error} onRetry={handleRetry} />
          </div>
        )}
        <button
          onClick={handleTransform}
          disabled={loading || !selectedFile || !selectedStyle}
          className="bg-arcane-accent hover:bg-purple-600 text-white font-bold py-3 px-10 rounded-full shadow-arcane transition-all duration-300 flex items-center justify-center mx-auto"
        >
          {loading ? (
            <>
              <Loader2 className="animate-spin mr-2" /> Transforming...
            </>
          ) : (
            <>
              <Wand2 className="mr-2" /> Transform Image
            </>
          )}
        </button>

        {transformedImage && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="mt-8"
          >
            <h3 className="text-2xl font-semibold mb-4">Transformed Image</h3>
            <Image src={transformedImage} alt="Transformed" width={500} height={500} className="max-w-full h-auto rounded-lg mx-auto shadow-lg" />
          </motion.div>
        )}
      </motion.div>
    </section>
  );
};

export default UploadZone;
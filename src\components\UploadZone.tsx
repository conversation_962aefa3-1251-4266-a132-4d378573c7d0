"use client";
import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Wand2Icon } from '@heroicons/react/24/outline';
import Image from 'next/image';
import { SpellCircleUpload } from './upload/SpellCircleUpload';
import { StyleSelectionSection } from './StyleSelectionSection';
import { TransformationResult } from './TransformationResult';
import ErrorDisplay from './ErrorDisplay';
import { GenerationProgress } from './ui/ProgressBar';
import { uploadImage, saveImageRecord } from '../lib/supabase';
import { getErrorMessage, parseApiError, AppError, ErrorType } from '../lib/errors';
import { validateImageFile, validateStyle, ALLOWED_STYLES } from '../lib/validation';
import { useAuth } from '@/lib/auth';

const UploadZone: React.FC = () => {
  const { user } = useAuth();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedStyle, setSelectedStyle] = useState<string | null>(null);
  const [transformedImage, setTransformedImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<AppError | null>(null);
  const [progress, setProgress] = useState(0);
  const [stage, setStage] = useState<'uploading' | 'processing' | 'complete' | 'error'>('uploading');

  const handleFileSelect = useCallback((file: File) => {
    setSelectedFile(file);
    setTransformedImage(null);
    setError(null);
    setProgress(0);
  }, []);

  const handleStyleSelect = useCallback((style: string) => {
    setSelectedStyle(style);
    setError(null);
  }, []);

  const handleError = useCallback((error: AppError) => {
    setError(error);
    setStage('error');
  }, []);

  // Simulate progress updates during transformation
  const simulateProgress = useCallback(() => {
    setProgress(0);
    setStage('uploading');

    // Upload phase
    const uploadInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 30) {
          clearInterval(uploadInterval);
          setStage('processing');

          // Processing phase
          const processInterval = setInterval(() => {
            setProgress(prev => {
              if (prev >= 100) {
                clearInterval(processInterval);
                setStage('complete');
                return 100;
              }
              return prev + Math.random() * 10;
            });
          }, 500);

          return 30;
        }
        return prev + Math.random() * 15;
      });
    }, 200);
  }, []);

  const handleTransform = async () => {
    // Validate inputs
    if (!selectedFile || !selectedStyle) {
      setError({
        type: ErrorType.VALIDATION_ERROR,
        message: 'Please select an image and a style.',
        retryable: false
      });
      return;
    }

    // Double-check file validation
    const fileValidation = validateImageFile(selectedFile);
    if (!fileValidation.isValid) {
      setError({
        type: ErrorType.VALIDATION_ERROR,
        message: 'Invalid file selected',
        details: fileValidation.errors.join(', '),
        retryable: false
      });
      return;
    }

    // Validate style
    const styleValidation = validateStyle(selectedStyle);
    if (!styleValidation.isValid) {
      setError({
        type: ErrorType.VALIDATION_ERROR,
        message: 'Invalid style selected',
        details: styleValidation.errors.join(', '),
        retryable: false
      });
      return;
    }

    setLoading(true);
    setError(null);
    setTransformedImage(null);
    simulateProgress();

    try {
      // Upload original image to Supabase
      const originalImageUrl = await uploadImage(selectedFile);

      const formData = new FormData();
      formData.append('image', selectedFile);
      formData.append('style', selectedStyle);

      const response = await fetch('/api/generate', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const apiError = parseApiError(response, errorData);
        setError(apiError);
        setStage('error');
        return;
      }

      const data = await response.json();
      const transformedImageUrl = data.image;

      setTransformedImage(transformedImageUrl);
      setStage('complete');
      setProgress(100);

      // Save record to database (use user ID if authenticated)
      try {
        await saveImageRecord(user?.id || null, originalImageUrl, transformedImageUrl, selectedStyle);
      } catch (dbError) {
        // Don't fail the whole operation if database save fails
        console.warn('Failed to save image record:', dbError);
        const dbErrorInfo = getErrorMessage(dbError);
        setError(dbErrorInfo);
      }

    } catch (err) {
      const errorInfo = getErrorMessage(err);
      setError(errorInfo);
      setStage('error');
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = () => {
    handleTransform();
  };

  return (
    <div id="upload-section" className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Error Display */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mb-8"
          >
            <ErrorDisplay error={error} onRetry={handleRetry} />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Progress Display */}
      <AnimatePresence>
        {loading && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="mb-8 flex justify-center"
          >
            <GenerationProgress
              stage={stage}
              progress={progress}
              estimatedTime={stage === 'processing' ? 15000 : undefined}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Upload Section */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-8"
        >
          <div className="text-center">
            <h2 className="text-3xl font-arcane font-bold text-textPrimary mb-4">
              Choose Your Image
            </h2>
            <p className="text-textSecondary">
              Upload an image to transform with mystical AI magic
            </p>
          </div>

          <SpellCircleUpload
            onFileSelect={handleFileSelect}
            selectedFile={selectedFile}
            disabled={loading}
            onError={handleError}
          />

          {selectedFile && !loading && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center"
            >
              <div className="glass-card p-4 rounded-xl inline-block">
                <Image
                  src={URL.createObjectURL(selectedFile)}
                  alt="Selected image preview"
                  width={200}
                  height={200}
                  className="rounded-lg object-cover"
                />
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* Style Selection */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="space-y-8"
        >
          <div className="text-center">
            <h2 className="text-3xl font-arcane font-bold text-textPrimary mb-4">
              Select Your Style
            </h2>
            <p className="text-textSecondary">
              Choose from our collection of mystical artistic transformations
            </p>
          </div>

          <StyleSelectionSection
            selectedStyle={selectedStyle}
            onStyleSelect={handleStyleSelect}
          />
        </motion.div>
      </div>

      {/* Transform Button */}
      {!loading && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-12 text-center"
        >
          <button
            onClick={handleTransform}
            disabled={!canTransform}
            className={`
              btn-primary text-lg px-8 py-4 rounded-2xl
              disabled:opacity-50 disabled:cursor-not-allowed
              hover:scale-105 active:scale-95
              transition-all duration-300
              ${canTransform ? 'shadow-glow' : ''}
            `}
          >
            <Wand2Icon className="w-5 h-5 mr-2" />
            Cast Transformation Spell
          </button>

          {!user && canTransform && (
            <p className="text-textMuted text-sm mt-3">
              Sign in to save your transformations to your private gallery
            </p>
          )}
        </motion.div>
      )}

      {/* Transformation Result */}
      <AnimatePresence>
        {transformedImage && !loading && (
          <TransformationResult
            transformedImage={transformedImage}
            style={selectedStyle || ''}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default UploadZone;
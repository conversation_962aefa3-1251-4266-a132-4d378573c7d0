// Import the centralized supabase client from auth.ts
import { supabase } from './auth';

// Check if we're in development mode
const isDevelopmentMode =
  !process.env.NEXT_PUBLIC_SUPABASE_URL ||
  process.env.NEXT_PUBLIC_SUPABASE_URL.includes('placeholder');

export async function uploadImage(file: File): Promise<string> {
  if (isDevelopmentMode || !supabase) {
    // In development mode, return a mock URL
    return `https://via.placeholder.com/500x500.png?text=${encodeURIComponent(file.name)}`;
  }

  const fileName = `${Date.now()}-${file.name}`;
  const { data, error } = await supabase.storage
    .from('images')
    .upload(fileName, file);

  if (error) throw new Error('Upload failed');

  const { data: { publicUrl } } = supabase.storage
    .from('images')
    .getPublicUrl(data.path);

  return publicUrl;
}

export async function saveImageRecord(userId: string | null, originalUrl: string, transformedUrl: string, style: string) {
  if (isDevelopmentMode || !supabase) {
    // In development mode, just log the action
    console.log('Development mode: Would save image record:', { userId, originalUrl, transformedUrl, style });
    return;
  }

  const { error } = await supabase
    .from('images')
    .insert({ user_id: userId, original_url: originalUrl, transformed_url: transformedUrl, style });

  if (error) throw new Error('Failed to save image record');
}

export interface ImageRecord {
  id: string;
  original_url: string;
  transformed_url: string;
  style: string;
  created_at: string;
}

export async function getImages(): Promise<ImageRecord[]> {
  if (isDevelopmentMode || !supabase) {
    // In development mode, return mock data
    return [
      {
        id: '1',
        original_url: 'https://via.placeholder.com/400x400.png?text=Original+1',
        transformed_url: 'https://via.placeholder.com/400x400.png?text=Mystic+Veil',
        style: 'Mystic Veil',
        created_at: new Date().toISOString()
      },
      {
        id: '2',
        original_url: 'https://via.placeholder.com/400x400.png?text=Original+2',
        transformed_url: 'https://via.placeholder.com/400x400.png?text=Shadow+Weave',
        style: 'Shadow Weave',
        created_at: new Date(Date.now() - 86400000).toISOString()
      }
    ];
  }

  const { data, error } = await supabase
    .from('images')
    .select('id, original_url, transformed_url, style, created_at')
    .order('created_at', { ascending: false });

  if (error) throw new Error('Failed to fetch images');
  return data || [];
}
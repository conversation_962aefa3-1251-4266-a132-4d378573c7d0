'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon, EnvelopeIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/lib/auth';
import { validateEmail } from '@/lib/validation';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'signin' | 'signup';
}

type AuthStep = 'email' | 'sent' | 'success';

export const AuthModal: React.FC<AuthModalProps> = ({
  isOpen,
  onClose,
  defaultTab = 'signin'
}) => {
  const { signIn, loading, error, clearError } = useAuth();
  const [email, setEmail] = useState('');
  const [step, setStep] = useState<AuthStep>('email');
  const [emailError, setEmailError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Clear previous errors
    clearError();
    setEmailError(null);

    // Validate email
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
      setEmailError(emailValidation.errors[0]);
      return;
    }

    try {
      await signIn(email);
      setStep('sent');
    } catch (error) {
      console.error('Sign in error:', error);
    }
  };

  const handleClose = () => {
    setStep('email');
    setEmail('');
    setEmailError(null);
    clearError();
    onClose();
  };

  const handleContinueAsGuest = () => {
    handleClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/60 backdrop-blur-sm"
          onClick={handleClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          className="relative w-full max-w-md mx-4 glass-card rounded-2xl p-6"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Close button */}
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 p-2 text-textMuted hover:text-textPrimary transition-colors focus-ring rounded-lg"
            aria-label="Close modal"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>

          {/* Content */}
          <div className="text-center">
            {step === 'email' && (
              <>
                <div className="mb-6">
                  <h2 className="text-2xl font-arcane font-bold text-textPrimary mb-2">
                    Enter the Arcane Realm
                  </h2>
                  <p className="text-textSecondary">
                    Sign in with your email to access your private gallery
                  </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="email" className="sr-only">
                      Email address
                    </label>
                    <div className="relative">
                      <EnvelopeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-textMuted" />
                      <input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Enter your email"
                        className={`
                          w-full pl-10 pr-4 py-3 glass-input rounded-lg text-textPrimary placeholder-textMuted
                          ${emailError || error ? 'border-crimson focus:border-crimson focus:ring-crimson/20' : ''}
                        `}
                        disabled={loading}
                        required
                      />
                    </div>
                    {emailError && (
                      <p className="text-crimson text-sm mt-1 text-left">{emailError}</p>
                    )}
                    {error && (
                      <p className="text-crimson text-sm mt-1 text-left">{error.message}</p>
                    )}
                  </div>

                  <button
                    type="submit"
                    disabled={loading || !email}
                    className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? (
                      <div className="flex items-center justify-center">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                        Sending magic link...
                      </div>
                    ) : (
                      'Send Magic Link'
                    )}
                  </button>
                </form>

                <div className="mt-6 pt-4 border-t border-glassBorder">
                  <button
                    onClick={handleContinueAsGuest}
                    className="text-cyan hover:text-cyan/80 text-sm underline transition-colors"
                  >
                    Continue as Guest
                  </button>
                </div>
              </>
            )}

            {step === 'sent' && (
              <>
                <div className="mb-6">
                  <div className="w-16 h-16 bg-cyan/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <EnvelopeIcon className="w-8 h-8 text-cyan" />
                  </div>
                  <h2 className="text-2xl font-arcane font-bold text-textPrimary mb-2">
                    Check Your Email
                  </h2>
                  <p className="text-textSecondary">
                    We've sent a magic link to <strong className="text-textPrimary">{email}</strong>
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="p-4 bg-cyan/10 border border-cyan/20 rounded-lg text-left">
                    <p className="text-sm text-textSecondary">
                      Click the link in your email to sign in. The link will expire in 1 hour.
                    </p>
                  </div>

                  <button
                    onClick={() => setStep('email')}
                    className="w-full btn-secondary"
                  >
                    Use Different Email
                  </button>

                  <button
                    onClick={handleContinueAsGuest}
                    className="text-cyan hover:text-cyan/80 text-sm underline transition-colors"
                  >
                    Continue as Guest
                  </button>
                </div>
              </>
            )}

            {step === 'success' && (
              <>
                <div className="mb-6">
                  <div className="w-16 h-16 bg-cyan/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircleIcon className="w-8 h-8 text-cyan" />
                  </div>
                  <h2 className="text-2xl font-arcane font-bold text-textPrimary mb-2">
                    Welcome to the Realm
                  </h2>
                  <p className="text-textSecondary">
                    You've successfully signed in!
                  </p>
                </div>

                <button
                  onClick={handleClose}
                  className="w-full btn-primary"
                >
                  Continue
                </button>
              </>
            )}
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

// Email validation function
function validateEmail(email: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!email) {
    errors.push('Email is required');
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.push('Please enter a valid email address');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

'use client';

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CloudArrowUpIcon,
  ExclamationTriangleIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';
import { CheckCircleIcon } from '@heroicons/react/24/solid';
import Image from 'next/image';
import { validateImageFile } from '@/lib/validation';
import { AppError, ErrorType } from '@/lib/errors';

interface SpellCircleUploadProps {
  onFileSelect: (file: File) => void;
  selectedFile?: File | null;
  disabled?: boolean;
  maxSize?: number;
  acceptedTypes?: string[];
  onError?: (error: AppError) => void;
  'aria-label'?: string;
}

export const SpellCircleUpload: React.FC<SpellCircleUploadProps> = ({
  onFileSelect,
  selectedFile,
  disabled = false,
  maxSize = 10 * 1024 * 1024, // 10MB
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp'],
  onError,
  'aria-label': ariaLabel = 'Upload image file'
}) => {
  const [dragError, setDragError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setDragError(null);

    // Handle rejected files
    if (rejectedFiles.length > 0) {
      const rejection = rejectedFiles[0];
      let errorMessage = 'Invalid file';
      
      if (rejection.errors.some((e: any) => e.code === 'file-too-large')) {
        errorMessage = `File too large. Maximum size: ${maxSize / (1024 * 1024)}MB`;
      } else if (rejection.errors.some((e: any) => e.code === 'file-invalid-type')) {
        errorMessage = 'Invalid file type. Please select an image file.';
      }
      
      setDragError(errorMessage);
      onError?.({
        type: ErrorType.VALIDATION_ERROR,
        message: errorMessage,
        retryable: false
      });
      return;
    }

    const file = acceptedFiles[0];
    if (file) {
      // Additional validation
      const validation = validateImageFile(file);
      if (!validation.isValid) {
        const errorMessage = validation.errors.join(', ');
        setDragError(errorMessage);
        onError?.({
          type: ErrorType.VALIDATION_ERROR,
          message: 'Invalid file selected',
          details: errorMessage,
          retryable: false
        });
        return;
      }

      onFileSelect(file);
    }
  }, [onFileSelect, onError, maxSize]);

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: acceptedTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
    maxSize,
    multiple: false,
    disabled
  });

  const hasError = dragError || isDragReject;
  const hasFile = selectedFile && !hasError;

  return (
    <div className="flex justify-center">
      <motion.div
        {...getRootProps()}
        className={`
          relative w-64 h-64 rounded-full border-4 border-dashed
          flex items-center justify-center cursor-pointer
          transition-all duration-300 ease-out overflow-hidden
          ${isDragActive && !isDragReject
            ? 'border-accent bg-accent/10 scale-105' 
            : hasError
            ? 'border-crimson bg-crimson/10'
            : hasFile
            ? 'border-cyan bg-cyan/10'
            : 'border-glassBorder hover:border-cyan hover:scale-102'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        whileHover={disabled ? {} : { scale: 1.02 }}
        whileTap={disabled ? {} : { scale: 0.98 }}
        aria-label={ariaLabel}
        role="button"
        tabIndex={disabled ? -1 : 0}
      >
        <input {...getInputProps()} aria-label={ariaLabel} />
        
        {/* Animated rune border */}
        <div className="absolute inset-0 rounded-full">
          <svg 
            className={`w-full h-full transition-all duration-1000 ${
              isDragActive ? 'animate-spin-slow' : hasFile ? 'animate-pulse-glow' : ''
            }`} 
            viewBox="0 0 100 100"
          >
            <circle
              cx="50"
              cy="50"
              r="48"
              fill="none"
              stroke="url(#runeGradient)"
              strokeWidth="1"
              strokeDasharray="10 5"
              opacity={hasError ? "0.3" : "0.6"}
            />
            <defs>
              <linearGradient id="runeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor={hasError ? "#dc2626" : hasFile ? "#38b2ac" : "#7e3ff2"} />
                <stop offset="50%" stopColor={hasError ? "#ef4444" : hasFile ? "#4fd1c7" : "#38b2ac"} />
                <stop offset="100%" stopColor={hasError ? "#dc2626" : hasFile ? "#38b2ac" : "#7e3ff2"} />
              </linearGradient>
            </defs>
          </svg>
        </div>

        {/* Content */}
        <div className="text-center z-10 p-4">
          <AnimatePresence mode="wait">
            {hasFile ? (
              <motion.div
                key="success"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="space-y-2"
              >
                <CheckCircleIcon className="w-12 h-12 text-cyan mx-auto" />
                <p className="text-sm text-textPrimary font-medium truncate max-w-[200px]">
                  {selectedFile.name}
                </p>
                <p className="text-xs text-textMuted">
                  {(selectedFile.size / (1024 * 1024)).toFixed(1)} MB
                </p>
              </motion.div>
            ) : hasError ? (
              <motion.div
                key="error"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="space-y-2"
              >
                <ExclamationTriangleIcon className="w-12 h-12 text-crimson mx-auto" />
                <p className="text-sm text-crimson font-medium">
                  Upload Error
                </p>
                <p className="text-xs text-crimson/80 max-w-[200px]">
                  {dragError || 'Invalid file type'}
                </p>
              </motion.div>
            ) : isDragActive ? (
              <motion.div
                key="active"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="space-y-2"
              >
                <CloudArrowUpIcon className="w-12 h-12 text-accent mx-auto animate-bounce" />
                <p className="text-sm text-accent font-medium">
                  Release to cast spell
                </p>
              </motion.div>
            ) : (
              <motion.div
                key="default"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="space-y-2"
              >
                <PhotoIcon className="w-12 h-12 text-textMuted mx-auto" />
                <p className="text-sm text-textSecondary">
                  Drop your image here
                </p>
                <p className="text-xs text-textMuted">
                  or click to browse
                </p>
                <p className="text-xs text-textMuted">
                  Max {maxSize / (1024 * 1024)}MB
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Preview overlay for selected file */}
        {selectedFile && !hasError && (
          <div className="absolute inset-4 rounded-full overflow-hidden opacity-20">
            <Image
              src={URL.createObjectURL(selectedFile)}
              alt="Preview"
              fill
              className="object-cover"
            />
          </div>
        )}
      </motion.div>
    </div>
  );
};

"use client";
import React from 'react';
import { motion } from 'framer-motion';
import { Wand2, Loader2 } from 'lucide-react';

interface TransformButtonProps {
  loading: boolean;
  disabled: boolean;
  onClick: () => void;
}

const TransformButton: React.FC<TransformButtonProps> = ({
  loading,
  disabled,
  onClick
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.4 }}
      className="text-center"
    >
      <button
        onClick={onClick}
        disabled={disabled}
        className="bg-arcane-accent hover:bg-purple-600 text-white font-bold py-3 px-10 rounded-full shadow-arcane transition-all duration-300 flex items-center justify-center mx-auto disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {loading ? (
          <>
            <Loader2 className="animate-spin mr-2" /> Transforming...
          </>
        ) : (
          <>
            <Wand2 className="mr-2" /> Transform Image
          </>
        )}
      </button>
    </motion.div>
  );
};

export default TransformButton;

@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-cinzel-decorative: "Cinzel Decorative";
    --font-manrope: "Manrope";
  }

  * {
    @apply border-glassBorder;
  }

  body {
    @apply bg-base text-textPrimary font-body;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-arcane text-textPrimary;
  }
}

@layer components {
  /* Glass morphism utilities */
  .glass-card {
    @apply bg-glass backdrop-blur-sm border border-glassBorder shadow-glass;
  }

  .glass-input {
    @apply bg-glass backdrop-blur-xs border border-glassBorder;
    @apply focus:border-cyan focus:ring-2 focus:ring-cyan/20;
    @apply transition-all duration-300;
  }

  /* Button variants */
  .btn-primary {
    @apply bg-accent hover:bg-accent/90 text-white;
    @apply px-6 py-3 rounded-lg font-medium;
    @apply transition-all duration-300;
    @apply focus:outline-none focus:ring-2 focus:ring-accent/50;
  }

  .btn-secondary {
    @apply bg-surface hover:bg-surface/80 text-textPrimary;
    @apply border border-glassBorder;
    @apply px-6 py-3 rounded-lg font-medium;
    @apply transition-all duration-300;
    @apply focus:outline-none focus:ring-2 focus:ring-cyan/50;
  }

  .btn-ghost {
    @apply hover:bg-glass text-textSecondary;
    @apply px-6 py-3 rounded-lg font-medium;
    @apply transition-all duration-300;
    @apply focus:outline-none focus:ring-2 focus:ring-cyan/50;
  }

  /* Hover effects */
  .hover-lift {
    @apply transition-all duration-300;
  }

  .hover-lift:hover {
    @apply -translate-y-1 scale-[1.02] shadow-lg;
  }

  /* Focus styles for accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-cyan focus:ring-offset-2 focus:ring-offset-base;
  }
}

/* Custom Masonry Grid Styles */
.my-masonry-grid {
  display: -webkit-box; /* Not needed if autoprefixing */
  display: -ms-flexbox; /* Not needed if autoprefixing */
  display: flex;
  margin-left: -30px; /* gutter size offset */
  width: auto;
}
.my-masonry-grid_column {
  padding-left: 30px; /* gutter size */
  background-clip: padding-box;
}

/* Style your items */
.my-masonry-grid_column > div { /* change div to all your item classes for masonry */
  margin-bottom: 30px;
}
